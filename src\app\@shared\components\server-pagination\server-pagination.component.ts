import { Component, EventEmitter, Input, Output } from '@angular/core';
import { BaseComponent } from '@core/utils';
import { PageChangedEvent } from 'ngx-bootstrap/pagination';
import { PaginationConfig } from '../../models';

@Component({
  selector: 'app-server-pagination',
  templateUrl: './server-pagination.component.html',
  styleUrls: ['./server-pagination.component.scss']
})
export class ServerPaginationComponent extends BaseComponent {
  @Input() paginationParams: PaginationConfig = new PaginationConfig();
  @Output() pageChanged: EventEmitter<PageChangedEvent> = new EventEmitter();

  onPageChanged($event: PageChangedEvent): void {
    this.paginationParams.page = $event.page;
    this.pageChanged.emit($event);
  }

  onItemsPerPageChange(itemsPerPage: number): void {
    this.paginationParams.itemsPerPage = itemsPerPage;
    this.pageChanged.emit({ page: 1, itemsPerPage });
  }

  get paginationInfo(): string {
    return `Showing ${this.paginationParams.number * this.paginationParams.itemsPerPage + (this.paginationParams.numberOfElements ? 1 : 0)}
          - ${(this.paginationParams.totalElements <= (this.paginationParams.itemsPerPage * (this.paginationParams.number + 1)) ? this.paginationParams?.totalElements : (this.paginationParams.number + 1) * this.paginationParams.itemsPerPage)}
          of ${this.paginationParams.totalElements}`;
  }
}
