import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { HistoryModuleName, MESSAGES, icons } from '@constants/*';
import { AppToasterService, CommonService as CommonSharedService } from '@core/services';
import { BaseComponent } from '@core/utils';
import { Role, UserCount } from '@pages/auth/models';
import { ConfirmationService } from 'primeng/api';
import { takeUntil } from 'rxjs';
import { RoleService } from '../../users/roles.service';

@Component({
  selector: 'app-role-list',
  templateUrl: './role-list.component.html',
  styleUrls: ['./role-list.component.scss']
})
export class RoleListComponent extends BaseComponent implements OnInit {

  roles!: Array<Role>;
  selectedRole!: Role | null;
  showCreateModal = false;
  redirectUrl!: string;
  showHistoryModal = false;
  historyModuleName = HistoryModuleName.ROLES_AND_PERMISSION;
  userCount!: Array<UserCount>;
  rolesAndPermission = 'Role & Permissions';
  isViewMode = false;

  constructor(
    private readonly roleService: RoleService,
    private readonly confirmationService: ConfirmationService,
    private readonly toasterService: AppToasterService,
    private readonly cdf: ChangeDetectorRef,
    private readonly activeRoute: ActivatedRoute,
    private readonly commonSharedService: CommonSharedService
  ) {
    super();
  }

  async ngOnInit(): Promise<void> {
    this.isLoading = true;
    await Promise.all([this.getUserCount(), this.getRoles()]);
    this.pageTitle = 'Roles & Permissions';
    this.listenToUrlParams();
  }

  listenToUrlParams(): void {
    this.activeRoute.queryParams
      .subscribe(params => {
        if (params?.id) {
          this.searchRoleById(Number(params.id));
        }
        if (params?.returnUrl) {
          this.redirectUrl = params?.returnUrl;
        }
      });
  }

  searchRoleById(id: number): void {
    const role = this.roles.find(role => role.id === id);
    this.onAddEditRole(role);
    this.commonSharedService.setBlockUI$(false);
  }

  async getRoles(): Promise<void> {
    this.isLoading = true;
    this.roleService.getList<Role>()
      .pipe(takeUntil(this.destroy$)).subscribe({
        next: (res: Array<Role>) => {
          this.roles = res;
          this.isLoading = false;
        }, error: () => {
          this.isLoading = false;
        }
      })
  }

  async getUserCount(): Promise<void> {
    this.roleService.getList<UserCount>('count')
      .pipe(takeUntil(this.destroy$)).subscribe({
        next: (res: Array<UserCount>) => {
          this.userCount = res;
        }
      })
  }

  updateRole(roleDetails: Role): void {
    const selectedRoleIndex = this.roles.findIndex((role: Role) => role.id === roleDetails.id);
    this.roles[selectedRoleIndex].name = roleDetails.name;
    this.roles[selectedRoleIndex].privilegeActionResponseDTOs = roleDetails.privilegeActionResponseDTOs;
    this.toasterService.success(MESSAGES.updateSuccessMessage.replace('{item}', this.rolesAndPermission));
  }

  addNewRole(role: Role) {
    this.roles.push(role);
    this.getUserCount();
    this.toasterService.success(MESSAGES.addSuccessMessage.replace('{item}', this.rolesAndPermission));
  }

  onViewRole(role: Role): void {
    this.selectedRole = role;
    this.showCreateModal = true;
    this.isViewMode = true;
    this.cdf.detectChanges();
  }

  onAddEditRole(role?: Role): void {
    if (role?.id) {
      this.selectedRole = role;
    }

    this.showCreateModal = true;
    this.cdf.detectChanges();
  }

  showDeleteModal(roleDetails: Role, event: Event): void {
    if (roleDetails?.systemDefault) {
      this.toasterService.warning(MESSAGES.disableDeleteRole);
    }

    else if (!roleDetails?.systemDefault) {
      this.confirmationService.confirm({
        target: event?.target as EventTarget,
        message: MESSAGES.deleteWarning.replace('{record}', 'Role & Permissions data'),
        icon: icons.triangle,
        header: "Confirmation",
        accept: () => {
          this.userCount.forEach(e => {
            if (e.id === roleDetails.id && e.userCount > 0) {
              this.toasterService.warning(`cannot delete ${roleDetails.name}, as active users are allocated with this role`);
            }
            else if (e.id === roleDetails.id && e.userCount <= 0) {
              this.onDeleteRole(roleDetails);
            }
          })
        }
      });
    }
  }

  onDeleteRole(roleDetails: Role): void {
    this.roleService.delete(roleDetails?.id).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.roles = this.roles.filter((role: Role) => role.id !== roleDetails.id);
        this.userCount = this.userCount.filter((userCount: UserCount) => userCount.id !== roleDetails.id);
        this.toasterService.success(MESSAGES.deleteSuccessMessage.replace('{item}', this.rolesAndPermission));
      }
    });
  }

  closeModal(): void {
    this.showCreateModal = false;
    this.isViewMode = false;
    this.selectedRole = {} as Role;
  }

  toggleHistorySidebar() {
    this.showHistoryModal = !this.showHistoryModal;
  }
}
