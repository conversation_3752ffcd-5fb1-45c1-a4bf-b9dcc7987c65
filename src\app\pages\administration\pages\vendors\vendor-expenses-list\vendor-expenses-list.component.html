<div class="modal-title">
  <h4>{{ vendorExpenseInfo?.name }}</h4>
  <fa-icon [icon]="faIcons.faTimes" (click)="onCancel()"></fa-icon>
</div>
<form [formGroup]="vendorExpenseFormGroup" (ngSubmit)="onSubmit()">
  <div class="content">
    <div class="title">
      <h4>Expenses</h4>
    </div>
    <div class="d-flex">
      <div formGroupName="startDateGroup">
        <label>From</label>
        <p-calendar
          appendTo="body"
          formControlName="value"
          placeholder="mm/dd/yyyy"
          [showIcon]="true"
          [showButtonBar]="true"
          [readonlyInput]="true"
          inputId="startDateIcon"
          [maxDate]="vendorEndDateFormGroup.controls?.value?.value"
        ></p-calendar>
      </div>
      <div formGroupName="endDateGroup" class="m-l-30">
        <label>To</label>
        <p-calendar
          appendTo="body"
          formControlName="value"
          placeholder="mm/dd/yyyy"
          [showIcon]="true"
          [showButtonBar]="true"
          [readonlyInput]="true"
          inputId="endDateIcon"
          [minDate]="vendorStartDateFormGroup.controls?.value?.value"
        ></p-calendar>
      </div>
      <div class="m-l-30 m-t-30">
        <label></label>
        <button class="btn btn-primary" [disabled]="vendorExpenseFormGroup.invalid">Go</button>
        <button class="btn btn-secondary m-l-20" type="button" (click)="reset()">Clear</button>
      </div>
    </div>
  </div>
  <div class="card tabs m-t-10">
    <div class="tab-content">
      <p-table
        class="no-column-selection"
        [value]="expenses"
        responsiveLayout="stack"
        sortMode="single"
        [customSort]="true"
        [lazy]="true"
        [reorderableColumns]="true"
        (onLazyLoad)="onSortChange($event, getAll.bind(this))"
        [sortField]="'id'"
        [rowHover]="true"
        [loading]="isLoading"
      >
        <ng-template pTemplate="header" let-columns>
          <tr>
            <th scope="col">PO/RO</th>
            <th scope="col">Invoice</th>
            <th scope="col">Date</th>
            <th scope="col">Type</th>
            <th scope="col">Description</th>
            <th scope="col">#Stock</th>
            <th scope="col">Amount</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-rowData let-rowIndex="rowIndex">
          <tr>
            <td>{{ rowData?.poRoNumber }}</td>
            <td>{{ rowData?.invoiceNumber }}</td>
            <td>{{ rowData?.transDate | date: constants.dateFormat }}</td>
            <td>{{ rowData?.expenseType?.name }}</td>
            <td>{{ rowData?.description }}</td>
            <td>{{ rowData?.stockNumber }}</td>
            <td>{{ rowData?.amount | currency: 'USD' : 'symbol': '1.2-2' }}</td>
          </tr>
        </ng-template>
        <ng-template pTemplate="emptymessage">
          <td [colSpan]="8" class="no-data">No data to display</td>
        </ng-template>
      </p-table>
      <app-server-pagination [paginationParams]="paginationConfig"></app-server-pagination>
    </div>
  </div>

  <div class="modal-footer">
    <button class="btn btn-secondary" type="button" (click)="onCancel()">Close</button>
  </div>
</form>
