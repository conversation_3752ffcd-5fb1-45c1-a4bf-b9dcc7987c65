import { NgxGpAutocompleteModule } from '@angular-magic/ngx-gp-autocomplete';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { UiSwitchConfig } from '@constants/*';
import { environment } from '@env/environment';
import { Loader } from '@googlemaps/js-api-loader';
import { PublicInventoriesModule } from '@pages/public-inventories/public-inventories.module';
import { GoogleMapModule } from '@sharedComponents/google-map/google-map.component.module';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { NgxMaskModule } from 'ngx-mask';
import { UiSwitchModule } from 'ngx-ui-switch';
import { CalendarModule } from 'primeng/calendar';
import { CardModule } from 'primeng/card';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { RadioButtonModule } from "primeng/radiobutton";
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { TabViewModule } from 'primeng/tabview';
import { TooltipModule } from 'primeng/tooltip';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { CrmCustomerRejectQuotationModule } from '../../crm-customer/crm-customer-inventory-wrapper/crm-customer-reject-quotation/crm-customer-reject-quotation.module';
import { CrmContactCustomerLeadComponent } from '../crm-contact-tabs-wrapper/crm-contact-customer-lead/crm-contact-customer-lead.component';
import { CrmContactDocumentsComponent } from '../crm-contact-tabs-wrapper/crm-contact-documents/crm-contact-documents.component';
import { CrmContactPreviouslyOwnedComponent } from '../crm-contact-tabs-wrapper/crm-contact-previously-owned/crm-contact-previously-owned.component';
import { CrmContactQuotationComponent } from '../crm-contact-tabs-wrapper/crm-contact-quotation/crm-contact-quotation.component';
import { CrmContactReminderAddComponent } from '../crm-contact-tabs-wrapper/crm-contact-reminders/crm-contact-reminder-add/crm-contact-reminder-add.component';
import { CrmContactRemindersComponent } from '../crm-contact-tabs-wrapper/crm-contact-reminders/crm-contact-reminders.component';
import { CrmContactTasksComponent } from '../crm-contact-tabs-wrapper/crm-contact-tasks/crm-contact-tasks.component';
import { CrmQuotationResendComponent } from '../crm-contact-tabs-wrapper/crm-quotation-resend/crm-quotation-resend.component';
import { CrmContactAddExtendedModule } from '../crm-customer-add-extended/crm-customer-add-extended.module';
import { CrmTaskAddExtendedModule } from '../crm-task-add-extended/crm-task-add-extended.module';
import { CrmContactAddComponent } from './crm-contact-add.component';

@NgModule({
  declarations: [
    CrmContactAddComponent,
    CrmContactQuotationComponent,
    CrmContactTasksComponent,
    CrmContactCustomerLeadComponent,
    CrmContactPreviouslyOwnedComponent,
    CrmContactRemindersComponent,
    CrmContactReminderAddComponent,
    CrmContactDocumentsComponent,
    CrmQuotationResendComponent,
  ],
  imports: [
    CommonModule,
    SharedComponentsModule,
    DirectivesModule,
    FontAwesomeIconsModule,
    SidebarModule,
    ReactiveFormsModule,
    FormsModule,
    TabViewModule,
    ConfirmPopupModule,
    ConfirmDialogModule,
    DropdownModule,
    CardModule,
    TableModule,
    CrmContactAddExtendedModule,
    CrmTaskAddExtendedModule,
    CrmCustomerRejectQuotationModule,
    GoogleMapModule,
    UiSwitchModule.forRoot(UiSwitchConfig),
    NgxGpAutocompleteModule,
    NgxMaskModule.forRoot(),
    CalendarModule,
    DialogModule,
    CheckboxModule,
    PublicInventoriesModule,
    TooltipModule,
    RadioButtonModule
],
  providers: [
    {
      provide: Loader,
      useValue: new Loader({
        apiKey: environment.googleMapApiKey,
        libraries: ['places']
      })
    }],
  exports: [CrmContactAddComponent, NgxMaskModule]
})

export class CrmContactAddModule { }
