<app-page-header [pageTitle]="pageTitle">
  <div class="top-header" headerActionBtn>
    <button class="btn btn-primary me-3 show-label big-label-btn" type="button" *appHasPermission="[permissionActions.VIEW_ACTIVITY]" (click)="toggleHistorySidebar()">
      <fa-icon [icon]="faIcons.faClockRotateLeft" class="btn-icon"></fa-icon>
      <span class="btn-label">Activities</span>
    </button>
    <button
      class="btn btn-primary left me-3 show-label"
      type="button"
      [appImageIconSrc]="constants.staticImages.icons.exportFile"
      (click)="excelDownloadMenu.toggle($event)"
      [disabled]="!paginationConfig.totalElements"
    >
      <span class="show-label">Export</span>
      <fa-icon [icon]="faIcons.faSpinner" [spin]="true" class="ms-3 mr--12" *ngIf="isExporting"></fa-icon>
    </button>
    <p-menu #excelDownloadMenu [model]="excelDownloadMenuItems" [popup]="true"> </p-menu>
    <button
      class="btn btn-primary left big-label-btn"
      (click)="onAdd()"
      [appImageIconSrc]="constants.staticImages.icons.addNew"
      *appHasPermission="[permissionActions.CREATE_CUSTOMER_LEAD]"
    >
      <span class="m-l-20" class="btn-label">Add New Inventory Preference</span>
    </button>
    <button class="btn btn-primary left column-btn show-label" (click)="toggleFilterSidebar()">
      <span class="show-label">Columns</span>
      <fa-icon [icon]="faIcons.faCaretDown"></fa-icon>
    </button>
  </div>
</app-page-header>

<div class="card tabs stock-truck-list">
  <div class="tab-content">
    <p-table
      [columns]="selectedColumns"
      styleClass="p-datatable-gridlines"
      [value]="crmCustomerLeadList"
      responsiveLayout="scroll"
      [tableStyle]="{ 'max-width': 'inherit' }"
      sortMode="single"
      [customSort]="true"
      [lazy]="true"
      [reorderableColumns]="true"
      (onLazyLoad)="onSortChange($event, getAll.bind(this))"
      [sortField]="'id'"
      [rowHover]="true"
      [loading]="isLoading"
      [resizableColumns]="true"
      columnResizeMode="expand"
    >
      <ng-template pTemplate="header" let-columns>
        <!-- heading row -->
        <tr>
          <ng-container *ngFor="let col of columns">
            <th pResizableColumn *ngIf="col.disable && col.name === 'Lead Id'" [pSortableColumn]="col?.key" pReorderableColumn [pReorderableColumnDisabled]="true" scope="col">
              Lead Id <p-sortIcon [field]="col?.key"> </p-sortIcon>
            </th>
            <th pResizableColumn [pSortableColumn]="col?.shortingKey" [pSortableColumnDisabled]="!col.shorting" pReorderableColumn *ngIf="!col.disable">
              {{ col.name }}
              <span *ngIf="col.name !== this.hideFieldForSearch">
                <p-sortIcon [field]="col.shortingKey || col.field" *ngIf="col.shorting"></p-sortIcon>
              </span>
            </th>
            <th pResizableColumn pReorderableColumn [pReorderableColumnDisabled]="true" class="cursor-default" *ngIf="col.disable && col.name === 'Action'">
              {{ col.name }}
            </th>
          </ng-container>
        </tr>
        <!-- Column search row -->
        <tr class="inventory-search-tr">
          <ng-container *ngFor="let col of columns">
            <th pResizableColumn *ngIf="col.disable && col.name === 'Lead Id'" scope="col">
              <span class="search-input"><input pInputText type="text" class="form-control" (input)="tableSearchByColumn($event.target, col)" [(ngModel)]="col.value" /> </span>
            </th>
            <th pResizableColumn *ngIf="!col.disable">
              <span class="search-input" *ngIf="col.type === 'DROP_DOWN'">
                <span *ngIf="col.shortingKey === 'status'">
                  <p-dropdown
                    appPreventClearFilter
                    [options]="statusCopyList"
                    [(ngModel)]="col.value"
                    (onChange)="tableSearchByColumn($event, col)"
                    class="w-130 customer-status"
                    optionLabel="name"
                    optionValue="name"
                    [filter]="true"
                    appendTo="body"
                    filterBy="name"
                    [showClear]="col.value ? true : false"
                    placeholder="Select status"
                  >
                    <ng-template pTemplate="selectedItem">
                      <div class="country-item country-item-value" *ngIf="col.value">
                        <div>{{ col.value }}</div>
                      </div>
                    </ng-template>
                    <ng-template let-status pTemplate="item">
                      <div class="country-item">
                        <div>{{ status.name }}</div>
                      </div>
                    </ng-template>
                  </p-dropdown>
                </span>
                <span *ngIf="col.shortingKey === 'unitTypeCategory.id'">
                  <p-dropdown
                    appPreventClearFilter
                    [options]="categoryTypes"
                    [(ngModel)]="selectedCategoryId"
                    (onFocus)="getCategoryTypes()"
                    (onChange)="tableSearchByColumn($event, col)"
                    class="w-130"
                    optionLabel="name"
                    optionValue="id"
                    [filter]="true"
                    filterBy="name"
                    [showClear]="selectedCategoryId ? true : false"
                    appendTo="body"
                    placeholder="Select Category"
                    (onClear)="onCategoryChange()"
                  >
                    <ng-template pTemplate="empty">
                      <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: dropdownLoaders.category, data: categoryTypes }"></ng-container>
                    </ng-template>
                  </p-dropdown>
                </span>
              </span>
              <span class="search-input" *ngIf="col.shortingKey === 'makes.make.id'">
                <p-multiSelect
                  appPreventClearFilter
                  [options]="makes"
                  placeholder="Select a Make"
                  optionLabel="name"
                  optionValue="id"
                  (onFocus)="getMake()"
                  [maxSelectedLabels]="1"
                  selectedItemsLabel="{0} items selected"
                  [(ngModel)]="makeIds"
                  (onChange)="tableSearchByColumn($event, col)"
                  appendTo="body"
                  (onPanelHide)="getModels()"
                >
                  <ng-template pTemplate="empty">
                    <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: dropdownLoaders.make, data: makes }"></ng-container>
                  </ng-template>
                </p-multiSelect>
                <fa-icon [icon]="faIcons.faXmark" class="cross-icon" *ngIf="makeIds?.length" (click)="clearMakes(col)"></fa-icon>
              </span>

              <span class="search-input" *ngIf="col.shortingKey === 'unitModels.unitModel.id'">
                <p-multiSelect
                  appPreventClearFilter
                  [options]="models"
                  placeholder="Select a Model"
                  optionLabel="name"
                  optionValue="id"
                  (onFocus)="getModels()"
                  [maxSelectedLabels]="1"
                  selectedItemsLabel="{0} items selected"
                  (onChange)="tableSearchByColumn($event, col)"
                  [(ngModel)]="modelIds"
                  appendTo="body"
                >
                  <ng-template pTemplate="empty">
                    <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: dropdownLoaders.model, data: models }"></ng-container>
                  </ng-template>
                </p-multiSelect>
                <fa-icon [icon]="faIcons.faXmark" class="cross-icon" *ngIf="modelIds?.length" (click)="clearModels(col)"></fa-icon>
              </span>

              <span class="search-input" *ngIf="col.shortingKey === 'unitType.id'">
                <p-multiSelect
                  appPreventClearFilter
                  [options]="unitTypes"
                  placeholder="Select a Unit Type"
                  optionLabel="name"
                  optionValue="id"
                  (onFocus)="getUnitType()"
                  [maxSelectedLabels]="1"
                  [(ngModel)]="unitTypeIds"
                  selectedItemsLabel="{0} items selected"
                  (onChange)="tableSearchByColumn($event, col)"
                  appendTo="body"
                >
                  <ng-template pTemplate="empty">
                    <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: dropdownLoaders.unitType, data: unitTypes }"></ng-container>
                  </ng-template>
                </p-multiSelect>
                <fa-icon [icon]="faIcons.faXmark" class="cross-icon" *ngIf="unitTypeIds?.length" (click)="clearUnitTypes(col)"></fa-icon>
              </span>
              <span class="search-input" *ngIf="(col.type === 'STRING' ) && col.name !== this.hideFieldForSearch">
                <input pInputText type="text" class="form-control" (input)="tableSearchByColumn($event.target, col)" [(ngModel)]="col.value" />
              </span>
              <span class="search-input" *ngIf="( col.type === 'INTEGER') && col.name !== this.hideFieldForSearch">
                <input pInputText type="number" class="form-control" (input)="tableSearchByColumn($event.target, col)" [(ngModel)]="col.value" />
              </span>
              <span class="search-input" *ngIf="col.type === 'DATE'">
                <p-calendar
                  appendTo="body"
                  [showIcon]="true"
                  [showButtonBar]="true"
                  [readonlyInput]="true"
                  inputId="startDateIcon"
                  (onSelect)="tableSearchByColumn($event, col)"
                  (onClearClick)="clearDate()"
                  [(ngModel)]="creationDate"
                ></p-calendar>
              </span>
            </th>
            <th pResizableColumn *ngIf="col.disable && col.name === 'Action'">
              <button type="button" class="btn btn-primary btn-sm reset-btn" (click)="clearSearchInput()">Reset filters</button>
            </th>
          </ng-container>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-soldData let-columns="columns" let-rowData let-rowIndex="rowIndex">
        <tr>
          <ng-container *ngFor="let col of columns">
            <td class="view-task" *ngIf="col.name === 'Lead Id' && col.disable" (click)="onViewEdit(soldData, false)">
              {{ getEvaluatedExpression(col.key, soldData) }}
            </td>
            <ng-container *ngIf="col.key && col.key !== null && !col.disable && col.type === 'MULTI_DROP_DOWN'">
              <td>
                <span *ngIf="col.name === 'Make' && col.type === 'MULTI_DROP_DOWN'">
                  <span *ngFor="let make of soldData.makes; let index = index">
                    <span class="view-inventory">
                      {{ make?.makeName }}
                    </span>
                    <span *ngIf="index !== soldData.makes.length - 1"> , </span>
                  </span>
                </span>
                <span *ngIf="col.name === 'Model' && col.type === 'MULTI_DROP_DOWN'">
                  <span *ngFor="let model of soldData.unitModels; let index = index">
                    <span class="view-inventory">
                      {{ model?.unitModelName }}
                    </span>
                    <span *ngIf="index !== soldData.unitModels.length - 1"> , </span>
                  </span>
                </span>
                <span *ngIf="col.name === 'Unit Type' && col.type === 'MULTI_DROP_DOWN'">
                  {{ getEvaluatedExpression(col.key, soldData) }}
                </span>
              </td>
            </ng-container>
            <ng-container *ngIf="col.key && col.key !== null && !col.disable && col.type !== 'DROP_DOWN' && col.type !== 'MULTI_DROP_DOWN'">
              <td>
                <span *ngIf="col.name === 'Customer' && col.type !== 'DATE' && col.name !== 'Category'">
                  {{ soldData?.contactName }}
                </span>
                <span *ngIf="col.type !== 'DATE' && col.name !== 'Category' && col.name !== 'Customer'">
                  {{ getEvaluatedExpression(col.key, soldData) }}
                </span>
                <span *ngIf="col.type === 'DATE'">
                  {{ getEvaluatedExpression(col.key, soldData) | date: constants.dateFormat }}
                </span>
              </td>
            </ng-container>
            <td *ngIf="col.type === 'DROP_DOWN'">
              <span *ngIf="col.name === 'Category' && col.type !== 'DATE' && col.name !== 'Customer'">
                {{ soldData?.category?.name }}
              </span>
              <p-dropdown
                *ngIf="col.shortingKey === 'status'"
                class="w-130 customer-status"
                appendTo="body"
                [options]="customerStatusList"
                (click)="findStatusIndex(rowIndex)"
                optionLabel="name"
                [(ngModel)]="rowData.status"
                optionValue="value"
                (onChange)="changeStatus(rowData?.status, rowData?.id)"
                [disabled]="!utils.hasSubModulePermission(userPermissions, [permissionActions.UPDATE_CUSTOMER_LEAD])"
              >
                {{ getStatusName(rowData?.status) }}
              </p-dropdown>
            </td>
            <td *ngIf="col.name === 'Action' && col.disable">
              <div class="actions-content">
                <img [src]="constants.staticImages.icons.edit" (click)="onViewEdit(rowData, true)" alt="" *appHasPermission="[permissionActions.UPDATE_CUSTOMER_LEAD]" />
                <img [src]="constants.staticImages.icons.deleteIcon" (click)="onDelete(rowData, $event)" alt="" *appHasPermission="[permissionActions.DELETE_CUSTOMER_LEAD]" />
              </div>
            </td>
          </ng-container>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <td [colSpan]="cols.length + 2" class="no-data">No data to display</td>
      </ng-template>
    </p-table>
    <app-server-pagination [paginationParams]="paginationConfig" (pageChanged)="onPageChanged($event)"> </app-server-pagination>
  </div>
</div>

<p-sidebar
  class="pipeline-config"
  [(visible)]="showCreateModal"
  [fullScreen]="true"
  (onHide)="showCreateModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
>
  <app-crm-customer-add
    (onClose)="onAddEditPopupClose($event)"
    *ngIf="showCreateModal"
    [crmCustomerInfo]="selectedCrmCustomerList"
    [isViewMode]="isCrmCustomerViewMode"
    [crmId]="crmId"
  >
  </app-crm-customer-add>
</p-sidebar>

<p-sidebar
  class="dealer"
  [(visible)]="showColumnModal"
  position="right"
  (onHide)="showColumnModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  appendTo="body"
  styleClass="p-sidebar-md"
>
  <app-column-dropdown
    (onClose)="toggleColumnSidebar()"
    *ngIf="showColumnModal"
    [isModelVisible]="showColumnModal"
    [privateFilter]="defaultColumnsArray"
    [filterParams]="getFilterSaveParams()"
    [columnsList]="dropDownColumnList"
    (onSubmitCheck)="callFilterApiAgain()"
  >
  </app-column-dropdown>
</p-sidebar>

<p-sidebar
  class="dealer"
  [(visible)]="showHistoryModal"
  position="right"
  (onHide)="showHistoryModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  appendTo="body"
  styleClass="p-sidebar-md"
>
  <app-recent-activity *ngIf="showHistoryModal" (onClose)="toggleHistorySidebar()" [modulesName]="historyModulesName"></app-recent-activity>
</p-sidebar>

<ng-template #emptyMessage let-loader="loader" let-data="data">
  <div class="d-flex justify-content-center">
    <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loader"></fa-icon>
    <p *ngIf="!loader && !data?.length">No records found</p>
  </div>
</ng-template>
<p-confirmPopup *ngIf="showConfirmPopup"></p-confirmPopup>
