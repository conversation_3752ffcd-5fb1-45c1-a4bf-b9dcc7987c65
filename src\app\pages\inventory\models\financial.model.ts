import { GenericFilterParams, IdNameModel } from "src/app/@shared/models";

export class FinancialInformation {
  retailAskingPrice!: number;
  wholeSalePrice!: number;
  acquisitionDate!: string;
  acquisitionCost!: number;
  grandTotal!: number;
  tax!: number;
  unitId!: number;
  acquisitionMethod!: IdNameModel;
  acquisitionMethodId!: number;
  purchasingAgent!: IdNameModel;
  purchasingAgentId!: number;
  salesPerson!: IdNameModel;
  id!: number;
  retailDispostionAmount!: number;
  wholeSaleDispostionAmount!: number;
  initialInvestment!: number;
  actualInvesment!: number;
  totalActualInvestment!: number;
  totalProjectedInvestment!: number;
  expense!: ExpensesDTODetail;
  netProfitAmount!: number;
  actualExpenses!: number;
}

export class ExpensesDTODetail {
  id!: number;
  poRoNumber!: string;
  invoiceNumber!: string;
  description!: string;
  amount!: number;
  transDate!: string;
  firstExpense!: boolean;
  vendor!: IdNameModel;
  supplier!: IdNameModel;
  expenseType!: IdNameModel;
  financialId!: number;
  unitId!: number;
  expensesAttachments!: ExpensesAttachment[];
  createdBy!: IdNameModel;
  purchasingAgent!: IdNameModel;
  acquisitionMethod!: IdNameModel;
  crmContact!: IdNameModel;
  ExpensesDTODetail!: string;
  contactAndVendorAndSupplierType!: string;
  expensesId?: number;
}

export class ExpensesListItem {
  id!: number;
  poRoNumber!: string;
  unitId!: number;
  invoiceNumber!: string;
  description!: string;
  vendor!: IdNameModel;
  supplier!: IdNameModel;
  stockNumber!: string;
  amount!: number;
  transDate!: string | null;
  endDate!: string | null;
  expenseType!: IdNameModel;
  documentUrl!: string;
  expensesAttachments: ExpensesAttachment[] = [];
  firstExpense!: boolean;
  contactAndVendorAndSupplierType!: string;
  constructor(json?: ExpensesListItem) {
    if (json) {
      Object.assign(this, json);
    }
  }
}

export class ExpensesListFilter extends GenericFilterParams {
  constructor(public unitId?: number, public vendorId?: number, public supplierId?: number) {
    super();
  }
}

export interface ExpensesCreateParams {
  poRoNumber: string;
  contactAndVendorAndSupplierType: string;
  invoiceNumber: string;
  transDate: string;
  description: string;
  vendorId?: number;
  supplierId?: number;
  amount: number;
  expenseTypeId: number;
  financialId: number;
  id: number;
  expensesAttachments: ExpensesAttachment[];
}

export class FinancialCreateParam {
  retailAskingPrice!: number;
  wholeSalePrice!: number;
  acquisitionDate!: string;
  acquisitionCost!: number;
  netProfitAmount!: number;
  tax!: number;
  unitId!: number;
  acquisitionMethodId!: number;
  purchasingAgentId!: number;
  id!: number;
}

export class ActualExpenses {
  actualExpenses!: number;
}

export interface ExpensesAttachment {
  fullExpensesAttachmentUrl?: string;
  id?: number;
  expensesId?: number;
  url: string;
  fileName?: string;
}

export enum financialTitle {
  retailAskingPrice = 'Retail Asking Price',
  wholeSalePrice = 'Wholesale Price'
}

export interface VendorSupplierBasicInfo {
  id: number;
  name: string;
  email: string;
  streetAddress: string;
  city: string;
  state: string;
  zipcode: string;
  latitude: number;
  longitude: number;
  type: string;
}