<div class="sale-parent-div">
  <form (ngSubmit)="onSubmit()">
    <section [formGroup]="holdDetailsFormGroup">
      <p-accordion class="nested-accordion" [multiple]="true">
        <div class="row">
          <div class="col-md-6">
            <p-accordionTab [(selected)]="accordionTabs.buyerInfo">
              <ng-template pTemplate="header">
                <div class="accordion-header" [ngClass]="{ active: accordionTabs.buyerInfo }">
                  <span>Buyer Information</span>
                  <em class="pi" [ngClass]="accordionTabs.buyerInfo ? 'pi-angle-up' : 'pi-angle-down'"></em>
                </div>
              </ng-template>
              <ng-template pTemplate="content">
                <ng-container [ngTemplateOutlet]="buyerInfoTemplate"></ng-container>
              </ng-template>
            </p-accordionTab>
          </div>
          <div class="col-md-6">
            <p-accordionTab [(selected)]="accordionTabs.salePersonInfo">
              <ng-template pTemplate="header">
                <div class="accordion-header" [ngClass]="{ active: accordionTabs.salePersonInfo }">
                  <span>Salesman Information</span>
                  <em class="pi" [ngClass]="accordionTabs.salePersonInfo ? 'pi-angle-up' : 'pi-angle-down'"></em>
                </div>
              </ng-template>
              <ng-template pTemplate="content">
                <ng-container [ngTemplateOutlet]="salesInfoTemplate"></ng-container>
              </ng-template>
            </p-accordionTab>
          </div>
          <div class="col-12">
            <p-accordionTab [(selected)]="accordionTabs.holdDetailsInfo">
              <ng-template pTemplate="header">
                <div class="accordion-header" [ngClass]="{ active: accordionTabs.holdDetailsInfo }">
                  <span>Hold Details</span>
                  <em class="pi" [ngClass]="accordionTabs.holdDetailsInfo ? 'pi-angle-up' : 'pi-angle-down'"></em>
                </div>
              </ng-template>
              <ng-template pTemplate="content">
                <ng-container [ngTemplateOutlet]="holdDetailsTemplate"></ng-container>
              </ng-template>
            </p-accordionTab>
          </div>
          <div class="col-md-12">
            <p-accordionTab [(selected)]="accordionTabs.notesInfo">
              <ng-template pTemplate="header">
                <div class="accordion-header" [ngClass]="{ active: accordionTabs.notesInfo }">
                  <span>Notes</span>
                  <em class="pi" [ngClass]="accordionTabs.notesInfo ? 'pi-angle-up' : 'pi-angle-down'"></em>
                </div>
              </ng-template>
              <ng-template pTemplate="content">
                <ng-container [ngTemplateOutlet]="notesTemplate"></ng-container>
              </ng-template>
            </p-accordionTab>
          </div>
        </div>
      </p-accordion>
    </section>
  </form>

  <ng-template #holdDetailsTemplate [formGroup]="holdDetailsFormGroup">
    <section>
      <div class="row">
        <div class="col-lg-4 col-md-6 col-12">
          <label>Deposit Date</label>
          <p-calendar appendTo="body" formControlName="depositDate" [showIcon]="true" [showButtonBar]="true" inputId="dateIcon"> </p-calendar>
          <div>
            <app-error-messages [control]="holdDetailsFormGroup.controls.depositDate"></app-error-messages>
          </div>
        </div>
        <div class="col-lg-4 col-md-6 col-12">
          <label>On Hold Until</label>
          <p-calendar appendTo="body" formControlName="onHoldUntil" [showIcon]="true" [showButtonBar]="true" inputId="dateIcon"> </p-calendar>
          <div>
            <app-error-messages [control]="holdDetailsFormGroup.controls.onHoldUntil"></app-error-messages>
          </div>
        </div>
        <div class="col-lg-4 col-md-6 col-12">
          <label>Deposit Amount</label>
          <p-inputNumber 
            styleClass="w-100" 
            inputStyleClass="form-control" 
            placeholder="Enter deposit amount"
            formControlName="depositAmount" 
            mode="currency" 
            currency="USD" 
            locale="en-US" />
          <app-error-messages [control]="holdDetailsFormGroup.controls.depositAmount"></app-error-messages>
        </div>
      </div>
    </section>
  </ng-template>

  <ng-template #buyerInfoTemplate [formGroup]="holdDetailsFormGroup">
    <div class="row">
      <div class="col-lg-12 col-md-12 col-12">
        <label class="required">Customer</label>
        <ng-container *appHasPermission="[permissionActions.CREATE_VENDORS]">
          <button
            class="btn btn-primary add-btn"
            id="addMakeBtn"
            type="button"
            *ngIf="!isViewMode"
            [appImageIconSrc]="constants.staticImages.icons.add"
            (click)="openContactModel()"
          ></button>
        </ng-container>
        <p-dropdown
          appendTo="body"
          [options]="contactList"
          formControlName="buyerInformationId"
          optionLabel="contactName"
          [showClear]="true"
          optionValue="id"
          [filter]="true"
          filterBy="contactName"
          (onFilter)="searchContact($event)"
          [virtualScroll]="true"
          [itemSize]="30"
          placeholder="Select contact"
          (onChange)="displayContactDetails($event.value)"
          [optionDisabled]="isOptionDisabled.bind(this)"
        >
          <ng-template pTemplate="empty">
            <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.previousOwnerName, data: contactList }"></ng-container>
          </ng-template>
          <ng-template let-item pTemplate="item">
            <span [ngClass]="{ 'disabled-dropdown-item': item.archived }">
              {{ item.contactName }}
              <span *ngIf="item.archived">{{ constants.archived }}</span>
            </span>
          </ng-template>
          <ng-template pTemplate="footer">
            <p *ngIf="!isLastPage" class="load-more" (click)="onLoadMore()">
              Load More <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loadMoreIcon"></fa-icon>
            </p>
          </ng-template>
        </p-dropdown>
        <app-error-messages [control]="holdDetailsFormGroup.controls.buyerInformationId"></app-error-messages>
      </div>
    </div>
    <div class="row mt-10">
      <div class="col-lg-12 col-md-12 col-12" *ngIf="displayContact">
        <p-card class="display-info">
          <p class="bold mb-1">{{ displayContact.name ? displayContact.name : displayContact.contactName }}</p>
          <p class="displayContactDetails company">{{ displayContact.company }}</p>
          <p class="displayContactDetails">{{ displayContact.primaryEmail }}</p>
          <p class="displayContactDetails">{{ displayContact.primaryPhone | phone }}</p>
        </p-card>
      </div>
    </div>
  </ng-template>

  <ng-template #salesInfoTemplate [formGroup]="holdDetailsFormGroup" ]>
    <div class="row">
      <div class="col-lg-12 col-md-12 col-12">
        <label class="required">Sales Person</label>
        <p-dropdown
          appPreventClearFilter
          appendTo="body"
          [options]="purchasingAgent"
          formControlName="salesPersonId"
          optionLabel="name"
          [showClear]="true"
          optionValue="id"
          [filter]="true"
          filterBy="stockNumber"
          placeholder="Select sales person"
          [virtualScroll]="true"
          [itemSize]="30"
          (onChange)="displaySalePersonDetails($event.value)"
        >
          <ng-template pTemplate="empty">
            <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.purchasingAgent, data: purchasingAgent }"></ng-container>
          </ng-template>
          <ng-template let-item pTemplate="item">
            <span>{{ item.name }}</span>
          </ng-template>
        </p-dropdown>
        <app-error-messages [control]="holdDetailsFormGroup?.controls?.salesPersonId"></app-error-messages>
      </div>
    </div>
    <div class="row mt-10">
      <div class="col-lg-12 col-md-12 col-12" *ngIf="displaySalesPerson">
        <p-card class="display-info">
          <p class="bold mb-1">{{ displaySalesPerson.name }}</p>
          <p class="displayContactDetails">{{ displaySalesPerson.email }}</p>
          <p class="displayContactDetails">{{ displaySalesPerson.phoneNumber | phone }}</p>
        </p-card>
      </div>
    </div>
  </ng-template>

  <ng-template #notesTemplate [formGroup]="holdDetailsFormGroup">
    <div class="row">
      <div class="col-12">
        <label>Notes</label>
        <textarea placeholder="Enter notes" rows="3" formControlName="notes"></textarea>
      </div>
    </div>
  </ng-template>
</div>

<ng-template #loaderTemplate>
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true"></fa-icon>
</ng-template>

<ng-template #emptyMessage let-loader="loader" let-data="data">
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loader"></fa-icon>
  <p *ngIf="!loader && !data?.length">No records found</p>
</ng-template>

<p-sidebar
  class="pipeline-config"
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="showCreateContact"
  position="right"
  (onHide)="showCreateContact = false"
  [blockScroll]="true"
  [showCloseIcon]="false"
  [fullScreen]="true"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-crm-contact-add (onClose)="onAddEditPopupClose($event)" *ngIf="showCreateContact"> </app-crm-contact-add>
</p-sidebar>
