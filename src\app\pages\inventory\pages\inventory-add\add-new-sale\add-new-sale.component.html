<div class="sale-parent-div">
  <form (ngSubmit)="onSubmit()">
    <section [formGroup]="saleFormGroup">
      <p-accordion class="nested-accordion" [multiple]="true">
        <div class="row">
          <div class="col-12">
            <p-accordionTab [(selected)]="accordionTabs.saleInfo">
              <ng-template pTemplate="header">
                <div class="accordion-header" [ngClass]="{ active: accordionTabs.saleInfo }">
                  <span>Sales Information</span>
                  <em class="pi" [ngClass]="accordionTabs.saleInfo ? 'pi-angle-up' : 'pi-angle-down'"></em>
                </div>
              </ng-template>
              <ng-template pTemplate="content">
                <ng-container [ngTemplateOutlet]="saleInfoTemplate"></ng-container>
              </ng-template>
            </p-accordionTab>
          </div>
          <div class="col-md-6">
            <p-accordionTab [(selected)]="accordionTabs.buyerInfo">
              <ng-template pTemplate="header">
                <div class="accordion-header" [ngClass]="{ active: accordionTabs.buyerInfo }">
                  <span>Buyer Information</span>
                  <em class="pi" [ngClass]="accordionTabs.buyerInfo ? 'pi-angle-up' : 'pi-angle-down'"></em>
                </div>
              </ng-template>
              <ng-template pTemplate="content">
                <ng-container [ngTemplateOutlet]="buyerInfoTemplate"></ng-container>
              </ng-template>
            </p-accordionTab>
          </div>
          <div class="col-md-6">
            <p-accordionTab [(selected)]="accordionTabs.salePersonInfo">
              <ng-template pTemplate="header">
                <div class="accordion-header" [ngClass]="{ active: accordionTabs.salePersonInfo }">
                  <span>Salesman Information</span>
                  <em class="pi" [ngClass]="accordionTabs.salePersonInfo ? 'pi-angle-up' : 'pi-angle-down'"></em>
                </div>
              </ng-template>
              <ng-template pTemplate="content">
                <ng-container [ngTemplateOutlet]="salesInfoTemplate"></ng-container>
              </ng-template>
            </p-accordionTab>
          </div>
          <div class="col-md-6">
            <p-accordionTab [(selected)]="accordionTabs.archiveSaleInfo">
              <ng-template pTemplate="header">
                <div class="accordion-header" [ngClass]="{ active: accordionTabs.archiveSaleInfo }">
                  <span>Archive Sale</span>
                  <em class="pi" [ngClass]="accordionTabs.archiveSaleInfo ? 'pi-angle-up' : 'pi-angle-down'"></em>
                </div>
              </ng-template>
              <ng-template pTemplate="content">
                <ng-container [ngTemplateOutlet]="archiveSaleInfoTemplate"></ng-container>
              </ng-template>
            </p-accordionTab>
          </div>
        </div>
      </p-accordion>
    </section>
  </form>

  <ng-template #saleInfoTemplate [formGroup]="saleFormGroup">
    <section>
      <div class="row">
        <div class="col-lg-3 col-md-4 col-12">
          <label class="required">Sales Type</label>
          <p-dropdown
            appPreventClearFilter
            appendTo="body"
            [options]="saleTypeList"
            formControlName="sellType"
            optionLabel="name"
            optionValue="value"
            [filter]="true"
            filterBy="name"
            [showClear]="true"
            placeholder="Select sale type"
          >
          </p-dropdown>
          <app-error-messages [control]="saleFormGroup.controls.sellType"></app-error-messages>
        </div>
        <div class="col-lg-3 col-md-4 col-12">
          <label>Quote</label>
          <p-dropdown
            appPreventClearFilter
            appendTo="body"
            [options]="quatationList"
            formControlName="quotationId"
            optionLabel="id"
            [showClear]="true"
            optionValue="id"
            [filter]="true"
            filterBy="id"
            placeholder="Select quote"
            (onChange)="selectQuotation($event.value)"
          >
            <ng-template pTemplate="empty">
              <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.previousOwnerName, data: quatationList }"></ng-container>
            </ng-template>
            <ng-template let-item pTemplate="item">
              <span>{{ item.id }}</span>
            </ng-template>
          </p-dropdown>
        </div>
        <div class="col-lg-2 col-md-4 col-12">
          <label>Sale Price</label>
          <p-inputNumber 
            styleClass="w-100" 
            inputStyleClass="form-control" 
            placeholder="Enter price"
            formControlName="sellPrice" 
            mode="currency" 
            currency="USD" 
            locale="en-US" />
          <app-error-messages [control]="saleFormGroup.controls.sellPrice"></app-error-messages>
        </div>

        <div class="col-lg-2 col-md-4 col-12">
          <label class="required">Invoice Date</label>
          <p-calendar appendTo="body" formControlName="invoiceDate" [showIcon]="true" [showButtonBar]="true" [readonlyInput]="true" inputId="dateIcon"> </p-calendar>
          <div>
            <app-error-messages [control]="saleFormGroup.controls.invoiceDate"></app-error-messages>
          </div>
        </div>
      </div>
    </section>
  </ng-template>

  <ng-template #buyerInfoTemplate [formGroup]="saleFormGroup">
    <div class="row">
      <div class="col-lg-12 col-md-12 col-12">
        <label class="required">Customer</label>
        <ng-container *appHasPermission="[permissionActions.CREATE_VENDORS]">
          <button
            class="btn btn-primary add-btn"
            id="addMakeBtn"
            type="button"
            *ngIf="!isViewMode"
            [appImageIconSrc]="constants.staticImages.icons.add"
            (click)="openContactModel()"
          ></button>
        </ng-container>
        <p-dropdown
          appendTo="body"
          [options]="contactList"
          formControlName="buyerInformationId"
          optionLabel="contactName"
          [showClear]="true"
          optionValue="id"
          [filter]="true"
          filterBy="contactName"
          (onFilter)="searchContact($event)"
          [virtualScroll]="true"
          [itemSize]="30"
          placeholder="Select contact"
          (onChange)="displayContactDetails($event.value)"
          [optionDisabled]="isOptionDisabled.bind(this)"
        >
          <ng-template pTemplate="empty">
            <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.previousOwnerName, data: contactList }"></ng-container>
          </ng-template>
          <ng-template let-item pTemplate="item">
            <span [ngClass]="{'disabled-dropdown-item': item.archived}">
              {{ item.contactName }}
              <span *ngIf="item.archived">{{ constants.archived }}</span>
            </span>
          </ng-template>
          <ng-template pTemplate="footer">
            <p *ngIf="!isLastPage" class="load-more" (click)="onLoadMore()">
              Load More <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loadMoreIcon"></fa-icon>
            </p>
          </ng-template>
        </p-dropdown>
        <app-error-messages [control]="saleFormGroup.controls.buyerInformationId"></app-error-messages>
      </div>
    </div>
    <div class="row mt-10">
      <div class="col-lg-12 col-md-12 col-12" *ngIf="displayContact">
        <p-card class="display-info">
          <p class="bold mb-1">{{ displayContact.name ? displayContact.name : displayContact.contactName }}</p>
          <p class="displayContactDetails company">{{ displayContact.company }}</p>
          <p class="displayContactDetails">{{ displayContact.primaryEmail }}</p>
          <p class="displayContactDetails">{{ displayContact.primaryPhone | phone }}</p>
        </p-card>
      </div>
    </div>
  </ng-template>

  <ng-template #salesInfoTemplate [formGroup]="saleFormGroup" ]>
    <div class="row">
      <div class="col-lg-12 col-md-12 col-12">
        <label class="required">Sales Person</label>
        <p-dropdown
          appPreventClearFilter
          appendTo="body"
          [options]="purchasingAgent"
          formControlName="salesPersonId"
          optionLabel="name"
          [showClear]="true"
          optionValue="id"
          [filter]="true"
          filterBy="stockNumber"
          placeholder="Select sales person"
          [virtualScroll]="true"
          [itemSize]="30"
          (onChange)="displaySalePersonDetails($event.value)"
        >
          <ng-template pTemplate="empty">
            <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: loaders.purchasingAgent, data: purchasingAgent }"></ng-container>
          </ng-template>
          <ng-template let-item pTemplate="item">
            <span>{{ item.name }}</span>
          </ng-template>
        </p-dropdown>
        <app-error-messages [control]="saleFormGroup?.controls?.salesPersonId"></app-error-messages>
      </div>
    </div>
    <div class="row mt-10">
      <div class="col-lg-12 col-md-12 col-12" *ngIf="displaySalesPerson">
        <p-card class="display-info">
          <p class="bold mb-1">{{ displaySalesPerson.name }}</p>
          <p class="displayContactDetails company">{{ saleInformation.dealerLocation }}</p>
          <p class="displayContactDetails">{{ displaySalesPerson.email }}</p>
          <p class="displayContactDetails">{{ displaySalesPerson.phoneNumber | phone }}</p>
        </p-card>
      </div>
    </div>
  </ng-template>

  <ng-template #archiveSaleInfoTemplate [formGroup]="saleFormGroup">
    <div class="row">
      <div class="col-12 d-flex">
        <div>
          <p-checkbox name="groupName" class="m-r-9" value="paymentCompleted" formControlName="paymentCompleted" label="Payment Completed" [binary]="true"></p-checkbox>
          <p-checkbox name="groupName" class="m-r-9" value="deliveredOrPickup" formControlName="deliveredOrPickup" label="Delivered/Picked Up" [binary]="true"></p-checkbox>
          <p-checkbox name="groupName" value="titleProcessed" formControlName="titleProcessed" label="Title Processed" [binary]="true"></p-checkbox>
        </div>
      </div>
    </div>
  </ng-template>
</div>

<ng-template #loaderTemplate>
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true"></fa-icon>
</ng-template>

<ng-template #emptyMessage let-loader="loader" let-data="data">
  <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loader"></fa-icon>
  <p *ngIf="!loader && !data?.length">No records found</p>
</ng-template>

<p-sidebar
  class="pipeline-config"
  [closeOnEscape]="false"
  [dismissible]="false"
  [(visible)]="showCreateContact"
  position="right"
  (onHide)="showCreateContact = false"
  [blockScroll]="true"
  [showCloseIcon]="false"
  [fullScreen]="true"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-crm-contact-add (onClose)="onAddEditPopupClose($event)" *ngIf="showCreateContact"> </app-crm-contact-add>
</p-sidebar>
