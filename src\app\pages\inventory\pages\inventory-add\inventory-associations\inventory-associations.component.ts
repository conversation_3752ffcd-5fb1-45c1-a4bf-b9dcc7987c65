import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { MESSAGES, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { AssociationHistory, AssociationsUnits, InventoryListItem } from '@pages/inventory/models';
import { InventoryAssociationsService } from '@pages/inventory/services';
import { AllUnitsAssociation } from '@pages/inventory/services/all-units.service';
import { GeneralInfoService } from '@pages/inventory/services/general-info.service';
import { InventoryService } from '@pages/inventory/services/inventory.service';
import { UnitDtoDetails } from '@pages/transport/models/incoming-truck.model';
import { IncomingTruckService } from '@pages/transport/services/incoming-truck.service';
import { ConfirmationService } from 'primeng/api';
import { Subject, debounceTime, takeUntil } from 'rxjs';
import { DataType, FilterValue, GenericFilterParams, IdNameModel, IdValueModel, OperatorType, TreeOperatorType } from 'src/app/@shared/models';

type inventoryInfoType = InventoryListItem | null | UnitDtoDetails | undefined;

@Component({
  selector: 'app-inventory-associations',
  templateUrl: './inventory-associations.component.html',
  styleUrls: ['./inventory-associations.component.scss']
})
export class InventoryAssociationsComponent extends BaseComponent implements OnInit, OnChanges {

  @Input() isViewMode!: boolean;
  @Input() inventoryInfo!: inventoryInfoType;
  @Input() selectedCategoryId!: number | null;

  @Output() onClose = new EventEmitter<boolean>();
  @Output() selectedUnitsList = new EventEmitter<Array<AssociationsUnits>>();

  stockList: Array<AssociationsUnits> = [];
  selectedType!: AssociationsUnits | undefined;
  selectedUnits: Array<AssociationsUnits> = [];
  associatedUnits!: Array<AssociationsUnits>;
  associationHistory!: Array<AssociationHistory>;
  categoryTypes!: Array<IdNameModel>;
  categoriesToShow!: Array<IdNameModel>;
  globalSearch = new Subject<FilterValue[]>();
  loaders = { association: false };
  loadMoreIcon = false;
  showCreateModal = false;
  isEditMode = false;
  pageNumber = 0;
  isLastPage = false;
  filterParams = new GenericFilterParams();
  accordionTabs = {
    history: true
  };
  associatedUnitIds: Number[] = [];
  displayOnWebInventory: IdValueModel[] = [];
  constructor(
    private readonly inventoryAssociationsService: InventoryAssociationsService,
    private readonly allUnitsAssociation: AllUnitsAssociation,
    private readonly confirmationService: ConfirmationService,
    private readonly cdf: ChangeDetectorRef,
    private readonly toasterService: AppToasterService,
    private readonly incomingTruckService: IncomingTruckService,
    private readonly inventoryService: InventoryService,
    private readonly generalInfoService: GeneralInfoService
  ) {
    super();
  }

  ngOnInit(): void {
    this.selectedCategoryId = Number(this.inventoryInfo?.generalInformation.unitTypeCategory?.id);
    this.setFilterParams();
    this.getUnitInventories();
    this.getAssociatedUnits();
    this.getAssociationHistory();
    this.getCategoryTypes();
    this.isEditMode = !!this.inventoryInfo?.id;
    this.displaySearchResult();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.selectedCategoryId?.currentValue) {
      this.setFilterParams();
      this.stockList = [];
      if (!this.inventoryInfo?.associations?.length) {
        this.associatedUnitIds = [this.selectedCategoryId ?? 0]
      }
      this.isEditMode = !!this.inventoryInfo?.id;
    }
    if (changes.selectedCategoryId?.currentValue === null) {
      this.categoryIsUnselected();
    }
  }

  searchStock(event: any): void {
    this.setFilteredStockParam(event.filter);
    this.globalSearch.next(this.filterParams.values);
  }

  displaySearchResult(): void {
    this.globalSearch.pipe(debounceTime(500)).subscribe(() => {
      this.stockList = [];
      this.pageNumber = 0;
      this.isLastPage = false;
      this.getUnitInventories();
    });
  }

  private setFilterParams(): void {
    this.filterParams = new GenericFilterParams();
    this.filterParams.values = [];
    this.filterParams.values.push(this.categoryParam(this.selectedCategoryId ?? 0));
    this.associatedUnitIds.push(this.selectedCategoryId ?? 0);
    this.setAssociationUnitFilter();

    this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP;
  }

  setAssociationUnitFilter(): void {
    if (this.inventoryInfo?.associations?.length) {
      this.inventoryInfo?.associations?.forEach(associatedUnit => {
        this.associatedUnitIds.push(associatedUnit.categoryId)
        this.filterParams.values.push(this.categoryParam(associatedUnit.categoryId))
      });
    }
  }

  categoryParam(id: number): FilterValue {
    return {
      dataType: DataType.LONG,
      key: 'generalInformation.unitTypeCategory.id',
      operator: OperatorType.NOT_EQUAL,
      value: id
    }
  }
  get stockParam(): FilterValue {
    return {
      dataType: DataType.STRING,
      key: 'generalInformation.stockNumber',
      operator: OperatorType.LIKE,
      value: ''
    }
  }

  get unitAssociationParam(): FilterValue {
    return {
      dataType: DataType.LONG,
      key: "unitAssociation.id",
      operator: OperatorType.NOT_EQUAL,
      value: ''
    }
  }

  getCategoryTypes() {
    this.inventoryService.getCategoryType().pipe(takeUntil(this.destroy$)).subscribe({
      next: (categoryTypes: Array<IdNameModel>) => {
        this.categoryTypes = categoryTypes;
        this.cdf.detectChanges();
      }
    });
  }

  categoryIsUnselected() {
    this.stockList = [];
  }

  onLoadMore(): void {
    if (!this.isLastPage) {
      this.loadMoreIcon = true;
      this.pageNumber++;
      this.getUnitInventories();
    }
  }

  private setFilteredStockParam(str: string): void {
    const stockParamValue = this.stockParam;
    stockParamValue.value = str;
    this.filterParams.values = this.filterParams?.values?.length ? this.filterParams?.values : [];
    const stockFilterValue = this.filterParams.values.find(value => value.key === 'generalInformation.stockNumber');
    if (stockFilterValue) {
      stockFilterValue.value = str;
    } else {
      this.filterParams.values.push(stockParamValue);
    }
  }

  private setAssociationId(id: number): void {
    this.filterParams.values = this.filterParams?.values?.length ? this.filterParams.values : [];
    const param = this.unitAssociationParam;
    param.value = id;
    this.filterParams.values.push(param);
  }

  getUnitInventories(id?: number): void {
    this.loaders.association = true;
    if (id) {
      this.setAssociationId(id);
    }
    if (this.filterParams?.values?.length) {
      this.filterParams.values = this.filterParams.values.filter(value => value.value)
    }
    this.filterParams.treeOperator = this.filterParams?.values?.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP;
    this.filterParams.isIncomingTruck = true;
    this.allUnitsAssociation.getListWithFiltersWithPagination<GenericFilterParams, AssociationsUnits>(this.filterParams, this.pageNumber + 1, 50).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res) => {
        this.isLastPage = res.last;
        this.stockList = [...this.stockList, ...res.content].filter(item => {
          if (this.associatedUnitIds.includes(item.categoryId)) {
            return false;
          }
          if (item.unitAssociationChildDTO && item.unitAssociationChildDTO.some(childDTO => (this.associatedUnitIds.includes(childDTO.categoryId)))) {
            return false;
          }
          return true;
        });
        if (id) {
          const selectedUnit = this.stockList.find(res => res.id === id)
          if (selectedUnit) {
            this.onUnitSelect(selectedUnit)
          }
        }
        if (this.inventoryInfo?.id) {
          this.inventoryService.setUnitId$(this.inventoryInfo.id);
        }
        this.isLoading = false;
        this.loaders.association = false;
        this.loadMoreIcon = false;
        this.cdf.detectChanges();
      },
      error: () => {
        this.isLoading = false;
        this.loaders.association = false;
        this.cdf.detectChanges();
      }
    })
  }

  getAssociationHistory(): void {
    if (this.inventoryInfo?.id) {
      this.inventoryAssociationsService.getAssociationHistory(this.inventoryInfo?.id).pipe(takeUntil(this.destroy$)).subscribe(
        {
          next: (res: Array<AssociationHistory>) => {
            this.associationHistory = res;
          }
        }
      )
    }
  }

  getAssociatedUnits(): void {
    if ((this.inventoryInfo as InventoryListItem)?.unitAssociationId || (this.inventoryInfo as InventoryListItem)?.unitAssociation?.id) {
      this.inventoryAssociationsService.get<Array<AssociationsUnits>>((this.inventoryInfo as InventoryListItem).unitAssociationId ?? Number((this.inventoryInfo as InventoryListItem).unitAssociation?.id)).pipe(takeUntil(this.destroy$)).subscribe(
        {
          next: (res: Array<AssociationsUnits>) => {
            this.associatedUnits = res.filter(associatedUnit => associatedUnit.id !== this.inventoryInfo?.id)
          }
        }
      )
    }
  }

  onUnitSelect(unit: AssociationsUnits) {
    if (this.inventoryInfo?.generalInformation.unitStatus.id === 2) {
      this.toasterService.error(MESSAGES.cantAssociatedForSoldInventory)
      return;
    }
    if (!this.selectedUnits.some(unitItem => {
      return unit.id === unitItem.id ||
        unitItem.unitAssociationChildDTO?.some((item: AssociationsUnits) => item.id === unit.id)
    })) {
      this.selectedUnits.push(unit);
      if (unit?.unitAssociationChildDTO?.length) {
        for (const unitIem of unit.unitAssociationChildDTO) {
          this.selectedUnits.push(unitIem);
        }
      }
      this.associatedUnitIds.push(unit?.categoryId)
      this.filterParams.values.push(this.categoryParam(unit?.categoryId))
      this.filterParams.values = this.filterParams.values.filter(value =>
        value.key !== 'generalInformation.stockNumber'
      );
      this.selectedUnitsList.emit(this.selectedUnits);
      this.getUnitInventories();
    } else {
      this.toasterService.info('Stock already added')
    }
    this.selectedType = {} as AssociationsUnits;
  }

  onSubmit(associatedId?: number): void {
    let parentUnitId = 0;
    this.inventoryService.getUnitId$().pipe(takeUntil(this.destroy$)).subscribe(res => {
      parentUnitId = res
    })
    this.onDisplayOnWebConfirmation();
    this.inventoryAssociationsService.patch({
      associationId: (this.inventoryInfo as InventoryListItem)?.unitAssociationId ?? associatedId,
      parentUnitCategoryId: this.inventoryInfo?.generalInformation.unitTypeCategory.id ?? this.selectedCategoryId,
      unitIds: this.selectedUnits.map(({ id }) => id),
      parentUnitId: parentUnitId
    }, API_URL_UTIL.inventory.association)
      .pipe(takeUntil(this.destroy$))
      .subscribe();
  }


  onRemoveAssociation(associatedUnit: AssociationsUnits, event: Event, removeSelectedUnit = false): void {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      header: 'Confirmation',
      message: MESSAGES.removeAssociation,
      icon: icons.triangle,
      accept: () => {
        this.removeAssociation(associatedUnit, removeSelectedUnit);
      }
    });
  }

  removeAssociation(associatedUnit: AssociationsUnits, removeSelectedUnit: boolean): void {
    if (removeSelectedUnit) {
      this.selectedUnits = this.selectedUnits.filter(associatedUnitItem => {
        return associatedUnit.id !== associatedUnitItem.id &&
          !associatedUnit.unitAssociationChildDTO?.some((item: AssociationsUnits) => item.id === associatedUnitItem.id)
      });
      this.selectedUnitsList.emit(this.selectedUnits);
      this.toasterService.info(MESSAGES.removedSuccessMessage.replace('{item}', 'Stock and associated stocks'));
    } else {
      this.inventoryAssociationsService.delete(
        associatedUnit?.id,
        `${API_URL_UTIL.inventory.remove}/${API_URL_UTIL.inventory.association}`
      ).pipe(takeUntil(this.destroy$)).subscribe({
        next: () => {
          this.associatedUnits = this.associatedUnits.filter(associatedUnitItem => {
            return associatedUnit.id !== associatedUnitItem.id;
          });
          this.toasterService.info(MESSAGES.removedSuccessMessage.replace('{item}', 'Stock'));
        }
      });
    }
    this.associatedUnitIds = this.associatedUnitIds.filter(au => au !== associatedUnit.categoryId);
    this.updateFilterParams(associatedUnit.categoryId);
    this.getUnitInventories();
    this.cdf.detectChanges();
  }

  updateFilterParams(categoryId: number) {
    this.filterParams.values = this.filterParams.values.filter(item => item.value !== categoryId);
  }

  getAssociatedUnitName(history: AssociationHistory, primaryStockNumber?: string | number): string {
    if (primaryStockNumber === history.unit.stockNumber) {
      return history.associatedUnit.stockNumber;
    }
    return history.unit.stockNumber;
  }

  getPrimaryInventoryImage(): string {
    return (this.inventoryInfo as InventoryListItem)?.unitImages?.fullUrl ?
      (this.inventoryInfo as InventoryListItem).unitImages?.fullUrl ?? '' :
      this.constants.staticImages.noImages;
  }

  onAdd() {
    this.categoriesToShow = this.categoryTypes;
    const selectedUnitsCategoryIds: number[] = this.selectedUnits.map(({ categoryId }) => categoryId);
    let alreadyPresentCategoryIds: number[] = [];
    let finalCategoryIds: number[] = [];

    if ((this.inventoryInfo as InventoryListItem)?.associations?.length) {
      alreadyPresentCategoryIds = (this.inventoryInfo as InventoryListItem)?.associations?.map(({ categoryId }) => categoryId) ?? [];
    }

    finalCategoryIds = [...new Set([...selectedUnitsCategoryIds, ...alreadyPresentCategoryIds])]

    this.categoriesToShow = this.categoriesToShow?.filter(
      categoryType =>
        Number(categoryType.id) !== this.selectedCategoryId && !finalCategoryIds?.includes(Number(categoryType.id))
    )

    if (this.categoriesToShow.length) {
      this.showCreateModal = true
    } else {
      this.toasterService.info('All categories are already associated with this category')
    }

  }

  onAddEditPopupClose(event: boolean) {
    this.showCreateModal = false;
    if (event) {
      this.getUnitInventories();
    }
  }

  onAddUnitId(unitId: number) {
    if (unitId) {
      this.isLoading = true;
      this.getUnitInventories(unitId)
    }
  }

  updateDisplayOnWeb(displayOnWeb: boolean, unitId: number): void {
    const selectedUnitCheckbox = this.selectedUnits.find(selectedUnit => selectedUnit.id === unitId)
    const selectedAssocitedCheckbox = this.associatedUnits.find(associatedUnit => associatedUnit.id === unitId)

    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: displayOnWeb ? MESSAGES.updateDisplayOnWebWarning.replace('{action}', 'want to') : MESSAGES.updateDisplayOnWebWarning.replace('{action}', 'dont want to'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        const displayOnWeb1 = this.displayOnWebInventory.find(wbv => wbv.id === unitId)
        if (displayOnWeb1 === undefined) {
          this.displayOnWebInventory.push({ id: unitId, value: displayOnWeb })
        } else {
          displayOnWeb1.value = !displayOnWeb;
        }
      },
      reject: () => {
        if (selectedUnitCheckbox) {
          selectedUnitCheckbox.internetOption.displayOnWeb = !selectedUnitCheckbox.internetOption.displayOnWeb;
        } else if (selectedAssocitedCheckbox) {
          selectedAssocitedCheckbox.internetOption.displayOnWeb = !selectedAssocitedCheckbox.internetOption.displayOnWeb;
        }
        this.cdf.detectChanges();
      }
    });
  }

  onDisplayOnWebConfirmation(): void {
    if (!this.displayOnWebInventory.length) {
      return
    }
    this.generalInfoService.updateDisplayOnWeb(this.displayOnWebInventory, `${API_URL_UTIL.inventory.internetOptions}`).pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.cdf.detectChanges();
        }
      });
  }
}
