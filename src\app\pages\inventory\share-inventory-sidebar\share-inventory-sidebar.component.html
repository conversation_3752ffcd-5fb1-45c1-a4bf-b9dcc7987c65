<div class="modal-title">
  <h4 class="header-title">{{ title }}</h4>
  <fa-icon [icon]="faIcons.faTimes" (click)="onClose(false)"></fa-icon>
</div>

<div class="content">
  <section class="mb-4">
    <div class="title">
      <h4>Email Details</h4>
    </div>
    <div class="row">
      <div class="col-12 mb-3">
        <div class="d-flex align-items-center justify-content-between w-100 mb-2">
          <label class="required">Email Address</label>
          <button type="button" class="btn btn-primary add-btn" [ngClass]="{ 'me-2': emails.length === 1 }" (click)="addEmailField()" title="Add another email">
            <i class="pi pi-plus"></i>
          </button>
        </div>
        <div *ngFor="let email of emails; let i = index; trackBy: trackByIndex" class="mb-2">
          <div class="d-flex align-items-center justify-content-between w-100">
            <input
              type="email"
              class="form-control me-2"
              [(ngModel)]="emails[i]"
              #emailField="ngModel"
              placeholder="Enter email address"
              [name]="'email_' + i"
              email
              required
              [class.is-invalid]="emailField.invalid && (emailField.touched || isFormSubmitted)"
            />
            <img
              [src]="constants.staticImages.icons.deleteIcon"
              alt="delete"
              (click)="removeEmailField(i)"
              *ngIf="emails.length > 1"
              title="Remove this email"
              class="cursor-pointer"
            />
          </div>
          <app-error-messages [control]="emailField" *ngIf="emailField.invalid && (emailField.touched || isFormSubmitted)"></app-error-messages>
        </div>
      </div>
    </div>
  </section>

  <section *ngIf="dealerGroups.length" class="mb-4">
    <div class="title">
      <h4>Sharable Inventories</h4>
    </div>
    <div *ngFor="let group of dealerGroups" class="card mb-3">
      <div class="card-body">
        <h6 class="card-title">{{ group.dealerName }}</h6>
        <div class="row">
          <div class="col-12">
            <label class="fw-bold">Public Link:</label>
            <div class="public-link">
              <a [href]="group.link" target="_blank" class="text-break">{{ group.link }}</a>
            </div>
          </div>
          <div class="col-12 mt-2">
            <label class="fw-bold">Stock Numbers:</label>
            <div class="stock-numbers">
              <span *ngFor="let sn of group.stockNumbers; let last = last" class="badge bg-primary me-1 mb-1">
                {{ sn }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <section *ngIf="nonSharableDealerGroups.length" class="mb-4">
    <div class="title">
      <h4>Non-Sharable Inventories</h4>
    </div>
    <div *ngFor="let group of nonSharableDealerGroups" class="card mb-3">
      <div class="card-body">
        <h6 class="card-title non-sharable-dealer-title">{{ group.dealerName }}</h6>
        <div class="non-sharable-items">
          <div *ngFor="let item of group.items" class="non-sharable-item mb-3">
            <div class="d-flex align-items-start">
              <span class="badge non-sharable-badge me-2">{{ item.stockNumber }}</span>
              <div class="reasons flex-grow-1">
                <ul class="list-unstyled mb-0">
                  <li *ngFor="let reason of item.reasons" class="text-muted small"><i class="fas fa-exclamation-circle me-1 non-sharable-icon"></i>{{ reason }}</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>

<div class="modal-footer">
  <button class="btn btn-secondary" type="button" (click)="onClose(true)">Cancel</button>
  <button class="btn btn-primary d-flex align-items-center" type="submit" (click)="onSend()">
    Send Email
    <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="isLoading"></fa-icon>
  </button>
</div>
