.add-btn {
  height: auto;
  padding: 5px 5px 0;
}

.title {
  h4 {
    color: var(--active-color);
    font-weight: 600;
    margin-bottom: 1rem;
    border-bottom: 2px solid var(--active-color);
    padding-bottom: 0.5rem;
  }
}

.stock-numbers {
  .badge {
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
  }
}

.card {
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;

  .card-title {
    color: var(--active-color);
    font-weight: 600;
    margin-bottom: 0.75rem;
  }

  &:hover {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  }
}

.public-link {
  background-color: var(--bs-light, #f8f9fa);
  border: 1px solid var(--bs-border-color, #dee2e6);
  border-radius: 0.25rem;
  padding: 0.5rem;

  a {
    color: var(--bs-primary, #0d6efd);
    text-decoration: none;
    font-size: 0.9rem;
    word-break: break-all;

    &:hover {
      text-decoration: underline;
      color: var(--bs-primary-dark, #0a58ca);
    }
  }
}

.non-sharable-dealer-title {
  color: var(--bs-secondary, #6c757d) !important;
  font-weight: 600;
}

.non-sharable-items {
  .non-sharable-item {
    padding: 0.5rem;
    background-color: var(--bs-light, #f8f9fa);
    border: 1px solid var(--bs-border-color, #dee2e6);
    border-radius: 0.25rem;
    border-left: 3px solid var(--bs-secondary, #6c757d);

    &:last-child {
      margin-bottom: 0 !important;
    }

    .non-sharable-badge {
      background-color: var(--bs-secondary, #6c757d) !important;
      color: white;
      font-size: 0.875rem;
      min-width: 60px;
      text-align: center;
    }

    .reasons {
      li {
        font-size: 0.875rem;
        line-height: 1.4;
        margin-bottom: 0.25rem;

        &:last-child {
          margin-bottom: 0;
        }

        .non-sharable-icon {
          color: var(--bs-secondary, #6c757d);
          font-size: 0.75rem;
        }
      }
    }
  }
}