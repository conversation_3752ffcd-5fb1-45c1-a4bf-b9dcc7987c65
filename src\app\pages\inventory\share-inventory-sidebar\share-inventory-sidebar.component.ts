import { Component, EventEmitter, Input, OnChanges, Output, QueryList, ViewChildren } from '@angular/core';
import { NgModel } from '@angular/forms';
import { MESSAGES } from '@constants/*';
import { AppToasterService } from '@core/services/toaster.service';
import { API_URL_UTIL, BaseComponent, faIcons } from '@core/utils';
import { takeUntil } from 'rxjs';
import { Constants } from 'src/app/@shared/constants/app.constants';
import { DealerGroup, InventoryItem, NonSharableDealerGroup, ShareEmailPayload } from './share-inventory-sidebar';
import { ShareInventoryService } from './share-inventory.service';

@Component({
  selector: 'app-share-inventory-sidebar',
  templateUrl: './share-inventory-sidebar.component.html',
  styleUrls: ['./share-inventory-sidebar.component.scss']
})
export class ShareInventorySidebarComponent extends BaseComponent implements OnChanges {
  @Input() sharableInventories: InventoryItem[] = [];
  @Input() nonSharableInventories: { item: InventoryItem; reasons: string[] }[] = [];
  @Input() domain: string = window.location.origin;
  @Output() close = new EventEmitter<boolean>();
  @Output() send = new EventEmitter<ShareEmailPayload>();
  
  @ViewChildren('emailField') emailFields!: QueryList<NgModel>;
  
  title = 'Share Inventory Links';
  emails: string[] = [''];
  isFormSubmitted = false;
  constants = Constants;
  isLoading = false;

  dealerGroups: DealerGroup[] = [];
  nonSharableDealerGroups: NonSharableDealerGroup[] = [];
  faIcons = faIcons;

  constructor(private toasterService: AppToasterService, private readonly shareInventoryService: ShareInventoryService,) {
    super();
  }

  trackByIndex(index: number): number {
    return index;
  }

  ngOnChanges() {
    this.dealerGroups = this.groupByDealer(this.sharableInventories);
    this.nonSharableDealerGroups = this.groupNonSharableByDealer(this.nonSharableInventories);
  }

  groupByDealer(inventories: InventoryItem[]): DealerGroup[] {
    const groups: { [dealer: string]: { stockNumbers: string[], dealerName: string } } = {};

    inventories.forEach(item => {
      if (!groups[item.dealerCode]) {
        groups[item.dealerCode] = {
          stockNumbers: [],
          dealerName: item.dealerName
        };
      }
      groups[item.dealerCode].stockNumbers.push(item.stockNumber);
    });

    return Object.keys(groups).map(dealerCode => ({
      dealer: dealerCode,
      dealerName: groups[dealerCode].dealerName,
      link: `${this.domain}/public-inventory/${dealerCode}?stockNumber=${groups[dealerCode].stockNumbers.join(',')}`,
      stockNumbers: groups[dealerCode].stockNumbers
    }));
  }

  groupNonSharableByDealer(nonSharableInventories: { item: InventoryItem; reasons: string[] }[]): NonSharableDealerGroup[] {
    const groups: { [dealer: string]: { dealerName: string, items: { stockNumber: string; reasons: string[] }[] } } = {};

    nonSharableInventories.forEach(nonSharable => {
      const dealerCode = nonSharable.item.dealerCode;
      if (!groups[dealerCode]) {
        groups[dealerCode] = {
          dealerName: nonSharable.item.dealerName,
          items: []
        };
      }

      groups[dealerCode].items.push({
        stockNumber: nonSharable.item.stockNumber,
        reasons: nonSharable.reasons
      });
    });

    return Object.keys(groups).map(dealerCode => ({
      dealer: dealerCode,
      dealerName: groups[dealerCode].dealerName,
      items: groups[dealerCode].items
    }));
  }

  addEmailField(): void {
    this.emails.push('');
    this.isFormSubmitted = false;
  }

  removeEmailField(index: number): void {
    if (this.emails.length > 1) {
      this.emails.splice(index, 1);
      this.isFormSubmitted = false;
    }
  }

  onSendShareEmail(payload: ShareEmailPayload): void {
    this.isLoading = true;
    this.shareInventoryService.add(payload, API_URL_UTIL.inventory.links).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.isLoading = false;
        this.toasterService.success(MESSAGES.publicLinksSentSuccess);
        this.onClose(true);
      }
    });
  }

  onSend(): void {
    this.isFormSubmitted = true;

    this.emailFields.forEach(field => {
      field.control.markAsTouched();
    });

    const hasInvalidFields = this.emailFields.some(field => field.invalid);

    if (hasInvalidFields) {
      this.toasterService.error(MESSAGES.invalidEmailAddress);
      this.isFormSubmitted = false;
      return;
    }

    const validEmails = this.emails.filter(email => email && email.trim() !== '');

    if (validEmails.length === 0) {
      this.toasterService.error(MESSAGES.atLeastOneEmailAddress);
      this.isFormSubmitted = false;
      return;
    }

    const payload: ShareEmailPayload = {
      emails: validEmails,
      links: this.dealerGroups.map(group => group.link)
    };

    this.onSendShareEmail(payload);

  }

  onClose(isClear?: boolean): void {
    if (isClear) {
      this.emails = [''];
    }
    this.isFormSubmitted = false;

    setTimeout(() => {
      this.emailFields?.forEach(field => {
        field.control.markAsUntouched();
        field.control.markAsPristine();
      });
    });

    this.close.emit(isClear);
  }
}

