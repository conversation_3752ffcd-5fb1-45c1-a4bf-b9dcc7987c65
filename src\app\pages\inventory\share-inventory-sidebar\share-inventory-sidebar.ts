export interface InventoryItem {
  stockNumber: string;
  dealerCode: string;
  dealerName: string;
  status: string;
  displayOnWeb: boolean;
}

export interface DealerGroup {
  dealer: string;
  dealerName: string;
  link: string;
  stockNumbers: string[];
}

export interface NonSharableDealerGroup {
  dealer: string;
  dealerName: string;
  items: { stockNumber: string; reasons: string[] }[];
}

export interface ShareEmailPayload {
  emails: string[];
  links: string[];
}