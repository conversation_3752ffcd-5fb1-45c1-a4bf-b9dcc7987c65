@import '/src/assets/scss/theme/mixins';

header {
  display: flex;
  align-items: center;
  background: var(--public-page-nav-color);
  box-shadow: var(--box-shadow);

  .logo {
    .filter-btn {
      width: auto !important;
      height: 34px !important;
      padding: 0 15px !important;
      border-radius: 9px !important;
      display: flex;
      align-items: center;
    }

    .search-wrapper {
      .header-search {
        height: 36px;
      }

      .input-group-text{
        border-top-right-radius: 9px !important;
        border-bottom-right-radius: 9px !important;
      }

      .form-control:focus {
        box-shadow: none !important;
      }
    }
  }
}

.page-wrapper {
  overflow: hidden;
  height: 100vh;

  .inventory-wrapper {
    height: calc(100vh - 100px);
    overflow: auto;
    width: 100%;
    --bs-gutter-x: 0;
    --bs-gutter-y: 0;

    .inventory-item-wrapper {
      overflow: auto;
      background-color: var(--public-page-bg-color);

      .invetory-content {
        text-align: center;

        img {
          width: 100%;
          height: auto;
          border-top-left-radius: 16px;
          border-top-right-radius: 16px;
        }

        .card {
          width: 266px;
          margin: -10px auto 0 auto;
          text-align: left;
          border-radius: 0px 0px 16px 16px;

          .card-body {
            .basic-details {
              .model-name,
              .model-description {
                color: var(--card-text-color);
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              .model-name {
                font-weight: 500;
                margin-bottom: 3px;
              }

              .model-description {
                color: #717171;
                margin-bottom: 10px;
              }

              .model-conditional-details {
                font-size: 12px;
                display: flex;
                justify-content: space-between;
                padding-bottom: 12px;
                border-bottom: 1px solid #d0d0d0;
                color: var(--card-text-color);

                .miles {
                  display: flex;
                  align-items: center;

                  img {
                    width: 14px;
                    height: 10px;
                    margin-right: 7px;
                  }
                }

                .matching-precentage {
                  display: flex;
                  align-items: center;

                  img {
                    width: 12px;
                    height: 12px;
                    margin-right: 7px;
                  }

                  span {
                    color: #2ca257;
                  }
                }
              }
            }

            .model-details {
              font-weight: 400;
              font-size: 12px;
              display: flex;
              justify-content: space-between;
              flex-wrap: wrap;
              margin-top: 15px;

              div {
                width: 50%;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                margin-bottom: 9px;

                .model-detail-label {
                  color: #939393;
                  margin-right: 2px;
                }
              }
              .model-detail-info {
                color: var(--card-text-color);
              }

              div:last-child {
                width: 100%;

                .model-detail-info {
                  font-weight: 500;
                  font-size: 16px;
                }
              }
            }
          }
        }
      }
    }

    .sort-wrapper {
      .total-inventories {
        font-size: 16px;
        color: black;
      }

      .sorting-dropdown {
        min-width: 200px;
      }
    }

    .selected-filter-wrapper {
      display: flex;
      flex-wrap: wrap;

      .item {
        border: 1px solid #92969c;
        border-radius: 17px;
        margin-bottom: 5px;
        padding: 0px 10px;
        margin-right: 5px;

        .name {
          font-size: 12px;
          margin-right: 7px;
        }

        .close {
          font-size: 12px;
        }
      }
    }
  }
}

.details-parent {
  .advertising-banner {
    position: absolute;
    bottom: 100%;
    left: 0;
    width: 150px;
    min-height: 30px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.DARK {
  .miles {
    img {
      filter: invert(1) !important;
    }
  }
}

::ng-deep .public-page {
  .card {
    background-color: #ffffff;
  }

  .p-dropdown {
    background-color: #ffffff !important;
  }

  .dropdown-wrapper {
    .p-dropdown {
      height: 36px !important;
      display: flex !important;
      align-items: center !important;
    }
  }

  .form-control,
  textarea {
    background-color: #ffffff !important;
    color: var(--card-text-color);
  }

  .p-calendar input {
    background-color: #ffffff !important;
    border-radius: 9px !important;
    color: var(--card-text-color);
  }

  .p-radiobutton .p-radiobutton-box {
    border: 2px solid #ced4da;
    background: #ffffff;
  }

  .p-checkbox .p-checkbox-box {
    border: 2px solid #ced4da;
    background: #ffffff;
  }

  .p-checkbox .p-checkbox-box .p-checkbox-icon {
    color: #ffffff;
  }

  .p-radiobutton .p-radiobutton-box .p-radiobutton-icon {
    background-color: #ffffff;
  }

  .server-pagination .items-per-page label,
  .server-pagination .items-per-page .pagination-info,
  .p-dropdown-label,
  .p-dropdown .p-dropdown-trigger,
  .server-pagination .page-link {
    color: var(--card-text-color);
  }
  .server-pagination .page-item.active .page-link {
    color: #ffffff !important;
    background-color: $public-page-active-color !important;
  }

  .server-pagination .pagination-prev a,
  .server-pagination .pagination-first a,
  .server-pagination .pagination-next a,
  .server-pagination .pagination-last a {
    color: var(--card-text-color) !important;
    background-color: var(--public-page-nav-color) !important;
  }

  footer {
    .version-info {
      color: var(--card-text-color) !important;
    }
    background-color: var(--public-page-nav-color);
    color: var(--card-text-color);
  }

  header .logo .dealer-name .p-dropdown {
    height: 36px;
    font-size: 14px;
    align-items: center;
  }
}

::ng-deep {
  .filter-sidebar {
    width: 25rem !important;

    .p-sidebar-content {
      background: var(--public-page-nav-color);
      padding-bottom: 1.5rem !important;
      height: 100vh;
    }

    p-inputnumber {
      width: 100% !important;

      .p-inputnumber {
        width: 100% !important;

        input {
          height: 36px;
          width: 100% !important;
          border-radius: 9px !important;
          border-color: var(--border-color-dark) !important;
        }
      }
    }

    .p-calendar input {
      border-radius: 9px !important;
      border-color: var(--border-color-dark) !important;
      height: 36px !important;
    }

    .p-sidebar-footer {
      display: none;
    }

    .filters-wrapper {
      .wrapper-section {
        .p-dropdown {
          height: 36px;
          font-size: 14px;
          align-items: center;
        }
      }

      .p-checkbox-label {
        color: var(--filter-text-color);
      }
    }

    .clear-all-btn {
      height: 36px !important;
      padding: 0 15px !important;
    }
  }
}

.global-loader-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;

  .global-loader-content {
    display: flex;
    flex-direction: column;
    align-items: center;

    .global-loader-icon {
      font-size: 3rem;
      color: $public-page-active-color;
      margin-bottom: 1rem;
    }

    .global-loader-text {
      font-size: 1.2rem;
      color: $public-page-active-color;
      font-weight: 500;
    }
  }
}

.slider-container {
  margin: 20px auto;
  width: 90%;
  .left-number {
    float: left;
  }
  .right-number {
    float: right;
  }
}

.filters-wrapper {
  .wrapper-section {
    padding: 14px 16px;
    border-bottom: 1px solid var(--border-color);

    .search-box {
      height: 36px;
      margin-bottom: 15px;
    }

    .more-records {
      font-weight: 500;
      font-size: 12px;
      line-height: 18px;
      color: var(--card-text-color);
      cursor: pointer;
    }

    .title {
      color: var(--public-filter-title-color);
    }

    .date-picker-wrapper,
    .price-wrapper {
      display: flex;
      align-items: center;
      color: var(--filter-text-color);

      .dash {
        margin: 20px 5px 0 5px;
      }

      .form-control {
        height: 36px;
        min-width: 150px !important;
        max-width: 250px !important;
      }

      label {
        color: var(--filter-text-color);
      }
    }
  }

  .title-wrapper {
    @include flex-space-between;

    span {
      color: var(--filter-text-color);
      font-weight: 500;
      font-size: 16px;
    }

    .clear-all {
      color: var(--card-text-color);
      cursor: pointer;
    }
  }

  .p-field-radiobutton {
    margin-bottom: 9px;

    p-radiobutton {
      margin-right: 10px;
    }

    label {
      color: var(--filter-text-color);
    }
  }

  .checkbox {
    margin-bottom: 9px;
    color: #000000;
    font-size: 14px;
    letter-spacing: -0.28px;
  }
}

@media screen and (max-width: 500px) {
  ::ng-deep {
    .filter-sidebar {
      width: 80vw !important;
    }
  }
}

@media screen and (max-width: 600px) {
  .page-wrapper {
    .inventory-wrapper {
      height: calc(100vh - 145px);
    }
  }
}

@media screen and (min-width: 500px) {
  .logo {
    .search-wrapper {
      .header-search {
        min-width: 250px !important;
      }
    }
  }
}

@media screen and (min-width: 1200px) {
  ::ng-deep {
    .filter-sidebar {
      width: 30rem !important;
    }
  }
}