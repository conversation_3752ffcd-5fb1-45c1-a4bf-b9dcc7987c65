import { DatePipe } from '@angular/common';
import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { dateFormat } from '@constants/*';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { ColumnItem } from '@pages/common-table-column/models/common-table.column.model';
import { ExpensesService } from '@pages/inventory/services/expenses.service';
import { InventoryService } from '@pages/inventory/services/inventory.service';
import { SoldTruckService } from '@pages/pipeline/pages/sold-truck-board/sold-truck.service';
import { ReportModuleConst } from '@pages/reports/models/reports-const';
import { ReportingService } from '@pages/reports/services/reporting-service';
import { PageChangedEvent } from 'ngx-bootstrap/pagination';
import { Subject, debounceTime, takeUntil } from 'rxjs';
import { DataType, FilterValue, GenericFilterParams, IdNameModel, OperatorType, TreeOperatorType } from 'src/app/@shared/models';
import { Utils } from 'src/app/@shared/services';
import { CommonService } from 'src/app/@shared/services/common.service';
import { ProfitabilityListItem } from '../../models/inventory-report-model';



@Component({
  selector: 'app-profitability-report',
  templateUrl: './profitability-report.component.html',
  styleUrls: ['./profitability-report.component.scss']
})
export class ProfitabilityReportComponent extends BaseComponent implements OnInit {
  _selectedColumns: ColumnItem[] = [];
  inventoryProfitList: ProfitabilityListItem[] = []
  filterParams: GenericFilterParams = new GenericFilterParams();
  globalSearch = new Subject<FilterValue[]>();
  dropDownColumnList: ColumnItem[] = [];
  selectedCategoryId!: number | null;
  categoryTypes!: Array<IdNameModel>;
  dropdownLoaders = {
    category: false,
  };
  constructor(
    private readonly commonService: CommonService,
    private readonly inventoryService: InventoryService,
    private readonly cdf: ChangeDetectorRef,
    private readonly expensesService: ExpensesService,
    private readonly soldTruckService: SoldTruckService,
    private readonly reportingService: ReportingService,
    private readonly datePipe: DatePipe,

  ) {
    super();
    this.pageTitle = 'Inventory Profitability Report';
    this.paginationConfig.itemsPerPage = 25;
  }

  onPageChanged($event: PageChangedEvent): void {
    this.getAll();
  }

  @Input()
  get selectedColumns(): any[] {
    return this._selectedColumns;
  }
  set selectedColumns(val: any[]) {
    this._selectedColumns = this.dropDownColumnList.filter(col => val.includes(col));
  }

  ngOnInit(): void {
    this.getDropDownColumnList();
    this.displaySearchResult();
    // this.getAllSearchOptions();
  }

  private displaySearchResult(): void {
    this.globalSearch.pipe(debounceTime(1000)).subscribe(() => {
      this.isLoading = true;
      this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP;
      this.getAll();
    });
  }

  getCategoryTypes() {
    if (!this.categoryTypes?.length) {
      this.dropdownLoaders.category = true
      this.inventoryService.getCategoryType().pipe(takeUntil(this.destroy$)).subscribe({
        next: (categoryTypes: Array<IdNameModel>) => {
          this.categoryTypes = categoryTypes;
          this.dropdownLoaders.category = false;
          this.cdf.detectChanges();
        }
      });
    }
  }

  private getDropDownColumnList(): void {
    const endpoint = `${API_URL_UTIL.columnMasters.root}${API_URL_UTIL.columnMasters.module}`.concat(`?module=${ReportModuleConst.INVENTORY_PROFITABILITY_REPORT}`);
    this.commonService.getListFromObject<ColumnItem[]>(endpoint).pipe(takeUntil(this.destroy$))
      .subscribe((response: any) => {
        this.dropDownColumnList = response;
        this._selectedColumns = response;
        this.cdf.detectChanges();
      });
  }

  getAll() {
    this.isLoading = true;
    this.filterParams.orderBy = this.orderBy;
    const endpoint = `${API_URL_UTIL.reports.inventoryProfit}/${API_URL_UTIL.reports.filter}`
    this.reportingService.getListWithFiltersWithPagination<GenericFilterParams, ProfitabilityListItem>(this.filterParams, this.paginationConfig.page, this.paginationConfig.itemsPerPage, endpoint)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.inventoryProfitList = res.content;
          this.setPaginationParamsFromPageResponse<ProfitabilityListItem>(res);
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
        }
      });
  }

  exportUsersToExcel(): void {
    this.isExporting = true;
    const endpoint = `${API_URL_UTIL.reports.inventoryProfit}/${API_URL_UTIL.reports.filter}`
    this.reportingService.getListWithFiltersWithPagination<GenericFilterParams, ProfitabilityListItem>
      (this.filterParams, 1, this.paginationConfig.totalElements, endpoint)
      .pipe(takeUntil(this.destroy$)).subscribe(res => {
        import("xlsx").then(xlsx => {
          const inventory = this.getExcelData(res.content)
          const worksheet = xlsx.utils.json_to_sheet(inventory);
          const workbook = { Sheets: { 'data': worksheet }, SheetNames: ['data'] };
          const excelBuffer: any = xlsx.write(workbook, { bookType: 'xlsx', type: 'array' });
          Utils.saveAsExcelFile(excelBuffer, "Profitability_Report");
          this.isExporting = false;
        });
      });
  }

  getExcelData(data: Array<ProfitabilityListItem>) {
    const excelData = data.map(res => ({
      'Stock#': res?.stockNumber,
      'Category': res?.category?.name,
      'Total Projected Investment': Utils.formatCurrency(res?.totalProjectedInvestment),
      'Acquisition Cost': Utils.formatCurrency(res?.initialInvestment),
      'Actual Expenses': Utils.formatCurrency(res?.actualInvestment),
      'Retail Asking Price': Utils.formatCurrency(res?.retailAskingPrice),
      'Sale Price': Utils.formatCurrency(res?.sellPrice),
      'Net Profit': Utils.formatCurrency(res?.netProfitAmount),
    }))
    return excelData;
  }

  clearSearchInput(): void {
    this.selectedColumns.forEach(cols => {
      cols.value = null;
    });
    this.dropDownColumnList.forEach(cols => {
      cols.value = null;
    });
    this.selectedCategoryId = null;
    this.filterParams.values = [];
    this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP
    this.getAll();
  }

  tableSearchByColumn(event: any, col: any): void {
    this.isLoading = true;
    this.inventoryProfitList = [];
    const searchInput = this.getEventValue(event, col);
    this.getFilterInfo(searchInput, col);
    this.setSearchEndDate(event, col);
    if (this.filterParams?.values?.length) {
      this.filterParams.values = this.filterParams.values.filter(values => {
        if (Array.isArray(values.value) && !values.value.length) {
          return false;
        }
        return true;
      });
    }
    this.globalSearch.next(this.filterParams.values);
    this.setValueForReset(searchInput, col);
  }

  private setValueForReset(input: string, col: ColumnItem): void {
    const temp = this.selectedColumns.find(d => d.key === col.key);
    const temp1 = this.dropDownColumnList.find(d => d.key === col.key);
    if (col.type === 'DATE') {
      temp.value = this.datePipe.transform(input, 'MM/dd/yyyy')
    } else {
      if (temp) {
        temp.value = input;
      }
    }
    if (temp1) {
      temp1.value = temp?.value;
    }

  }

  private getEventValue(event: any, col: ColumnItem): string {
    let temp = '';
    switch (col.type) {
      case 'DROP_DOWN':
        temp = col?.searchKey === 'status' ? event?.value?.split()?.join('_')?.toUpperCase() : event?.value
        break;
      case 'DATE':
        const endDate: any = this.datePipe.transform(event, dateFormat.format)
        temp = this.setStartDate(new Date(endDate).toISOString())
        break;
      case 'MULTI_DROP_DOWN':
        temp = event?.value
        break;
      default:
        temp = event.value
        break;
    }
    return temp
  }

  private setStartDate(endDate: string): string {
    const date = new Date(endDate);
    return new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0)).toISOString();
  }

  private getFilterInfo(inputValue: any, col: any): void {
    this.filterParams.values = this.filterParams.values === undefined ? [] : this.filterParams.values
    const existingValue = this.filterParams.values.find((f: any) => f.key === col.searchKey)
    if (!existingValue && inputValue) {
      this.filterParams.values.push({
        dataType: this.assignDataType(col) as DataType,
        key: col.searchKey,
        operator: this.assignOperator(col.type) as OperatorType,
        value: inputValue,
        enumName: col.enumKey
      })
    } else {
      if (existingValue) {
        if (inputValue) {
          existingValue.value = inputValue;
        } else {
          this.filterParams.values.splice(this.filterParams.values.indexOf(existingValue), 1)
        }
      }
    }
  }

  private assignOperator(type: string): string {
    let operatorType = '';
    switch (type) {
      case 'INTEGER':
      case 'DOUBLE':
      case 'DROP_DOWN':
        operatorType = OperatorType.EQUAL
        break;
      case 'DATE':
        operatorType = OperatorType.GREATER_THAN_OR_EQUAL
        break;
      case 'MULTI_DROP_DOWN':
        operatorType = OperatorType.IN
        break;
      default:
        operatorType = OperatorType.LIKE
        break;
    }
    return operatorType
  }


  private assignDataType(col: ColumnItem): string {
    let stringDataType = '';
    switch (col.type) {
      case 'boolean':
        stringDataType = DataType.BOOLEAN;
        break;
      case 'INTEGER':
        stringDataType = DataType.INTEGER;
        break;
      case 'DOUBLE':
        stringDataType = DataType.DOUBLE;
        break;
      case 'DATE':
        stringDataType = DataType.DATE;
        break;
      case 'DROP_DOWN':
        stringDataType = col?.searchKey === 'status' ? DataType.ENUM : DataType.INTEGER
        break;
      case 'MULTI_DROP_DOWN':
        stringDataType = DataType.LONG;
        break;
      default:
        stringDataType = DataType.STRING;
        break;
    }
    return stringDataType
  }

  private setSearchEndDate(event: any, col: any): void {
    const existingDate = this.filterParams.values.find((d: any) => d.key === col.key && d.operator === OperatorType.LESS_THAN_OR_EQUAL)
    if (existingDate) {
      this.filterParams.values.splice(this.filterParams.values.indexOf(existingDate), 1)
    }
    if (col.type === 'DATE') {
      const startDate: any = this.datePipe.transform(event, dateFormat.format)
      this.filterParams.values.push({
        dataType: this.assignDataType(col) as DataType,
        key: col.key,
        operator: OperatorType.LESS_THAN_OR_EQUAL,
        value: this.setEndDate(new Date(startDate).toISOString()),
        enumName: col.enumKey
      })
    }
  }

  private setEndDate(endDate: string): string {
    const date = new Date(endDate);
    return new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 50)).toISOString();
  }

  clearDate() {
    this.filterParams.values = this.filterParams.values.filter((param:any) => param.key !== "createdDate"); // Need to change createdDate to date parameter key
    this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP
    this.getAll();
  }
}
