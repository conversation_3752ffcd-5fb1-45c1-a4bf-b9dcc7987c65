<app-page-header [pageTitle]="pageTitle">
  <div headerActionBtn class="top-header">
    <button
      class="btn btn-primary left me-3 show-label"
      type="button"
      [appImageIconSrc]="constants.staticImages.icons.exportFile"
      (click)="exportUsersToExcel()"
      [disabled]="!paginationConfig.totalElements"
    >
      <span class="show-label">Export</span>
      <fa-icon [icon]="faIcons.faSpinner" [spin]="true" class="ms-3 mr--12" *ngIf="isExporting"></fa-icon>
    </button>
  </div>
</app-page-header>
<div class="card tabs stock-truck-list" [ngClass]="{ 'pb-4': !supplierExpenseFormGroup.value.supplierId }">
  <form [formGroup]="supplierExpenseFormGroup" (ngSubmit)="onSubmit()">
    <div class="content d-flex align-items-center ms-4">
      <div>
        <label>Supplier</label>
        <p-dropdown
          appPreventClearFilter
          [options]="suppliers"
          formControlName="supplierId"
          optionLabel="name"
          optionValue="id"
          [filter]="true"
          filterBy="name"
          appendTo="body"
          placeholder="Select Supplier"
          [virtualScroll]="true"
          [itemSize]="30"
          (onChange)="onSubmit()"
        >
          <ng-template pTemplate="empty">
            <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{ loader: dropdownLoaders.supplier, data: suppliers }"></ng-container>
          </ng-template>
        </p-dropdown>
      </div>
      <div class="d-flex align-items-center ms-4">
        <div formGroupName="startDateGroup">
          <label>From</label>
          <p-calendar
            appendTo="body"
            formControlName="value"
            placeholder="mm/dd/yyyy"
            [showIcon]="true"
            [showButtonBar]="true"
            [readonlyInput]="true"
            inputId="startDateIcon"
            [maxDate]="supplierEndDateFormGroup.controls?.value?.value"
          ></p-calendar>
        </div>
        <div formGroupName="endDateGroup" class="ms-3">
          <label>To</label>
          <p-calendar
            appendTo="body"
            formControlName="value"
            placeholder="mm/dd/yyyy"
            [showIcon]="true"
            [showButtonBar]="true"
            [readonlyInput]="true"
            inputId="endDateIcon"
            [minDate]="supplierStartDateFormGroup.controls?.value?.value"
          ></p-calendar>
        </div>
        <div class="ms-3 mt-4">
          <button class="btn btn-primary">Go</button>
          <button type="button" class="btn btn-primary btn-sm ms-2" (click)="clearSearchInput()">Reset filters</button>
        </div>
      </div>
    </div>
  </form>
  <div class="tab-content" *ngIf="supplierExpenseFormGroup.value.supplierId">
    <p-table
      [columns]="selectedColumns"
      styleClass="p-datatable-gridlines"
      [value]="expenses"
      responsiveLayout="scroll"
      sortMode="single"
      [customSort]="true"
      [lazy]="true"
      [reorderableColumns]="true"
      (onLazyLoad)="onSortChange($event, getAll.bind(this))"
      [sortField]="'id'"
      [rowHover]="true"
      [loading]="isLoading"
      [resizableColumns]="true"
      columnResizeMode="expand"
      class="has-date-range-filter"
    >
      <ng-template pTemplate="header" let-columns>
        <tr>
          <ng-container *ngFor="let col of columns">
            <th pResizableColumn pReorderableColumn [pSortableColumn]="col?.shortingKey" scope="col">{{ col.name }} <p-sortIcon [field]="col?.shortingKey"></p-sortIcon></th>
          </ng-container>
        </tr>
        <tr class="report-search-tr">
          <ng-container *ngFor="let col of columns">
            <th pResizableColumn scope="col">
              <span class="search-input" *ngIf="col.type === 'STRING'">
                <input pInputText placeholder="Search {{ col.name }}" type="text" class="form-control" (input)="tableSearchByColumn($event.target, col)" [(ngModel)]="col.value" />
              </span>
              <span class="search-input" *ngIf="col.type === 'DOUBLE'">
                <input pInputText placeholder="Search {{ col.name }}" type="number" class="form-control" (input)="tableSearchByColumn($event.target, col)" [(ngModel)]="col.value" />
              </span>
              <span class="search-input" *ngIf="col.type === 'DATE'">
                <p-calendar appendTo="body" [showIcon]="true" [showButtonBar]="true" [readonlyInput]="true" placeholder="mm/dd/yyyy" inputId="startDateIcon" (onSelect)="tableSearchByColumn($event, col)" (onClearClick)="clearDate()" [(ngModel)]="purchasedDate"></p-calendar>
              </span>
            </th>
          </ng-container>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-saleData let-columns="columns" let-rowData let-rowIndex="rowIndex">
        <tr>
          <ng-container *ngFor="let col of columns">
            <td>
              <span *ngIf="col.type !== 'DATE' && col.type !== 'DOUBLE'">
                {{ getEvaluatedExpression(col.key, saleData) }}
              </span>
              <span *ngIf="col.type === 'DATE'">
                {{ getEvaluatedExpression(col.key, saleData) | date: constants.dateFormat }}
              </span>
              <span *ngIf="col.type === 'DOUBLE'">
                {{ getEvaluatedExpression(col.key, saleData) | currency: 'USD' : 'symbol': '1.2-2' }}
              </span>
            </td>
          </ng-container>
        </tr>
      </ng-template>
      <ng-template pTemplate="summary">
        <div>Total Expense : {{ totalExpenseAmount | currency: 'USD' : 'symbol': '1.2-2' }}</div>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <td [colSpan]="dropDownColumnList.length + 2" class="no-data">No data to display</td>
      </ng-template>
    </p-table>
    <app-server-pagination [paginationParams]="paginationConfig" (pageChanged)="onPageChanged($event)"> </app-server-pagination>
  </div>
</div>
<ng-template #emptyMessage let-loader="loader" let-data="data">
  <div class="d-flex justify-content-center">
    <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loader"></fa-icon>
    <p *ngIf="!loader && !data?.length">No records found</p>
  </div>
</ng-template>
