import { AssociationsUnits, IdName, InternetOption, Odometer, PreviousOwner, UnitLotLocation } from "@pages/inventory/models";
import { DataType, GenericFilterParams, IdNameModel, OperatorType, TreeOperatorType } from "src/app/@shared/models";
const IN_TRANSIT = 'In Transit';
const AWAITING_TRANSPORT = 'Awaiting Transport';

export class IncomingTruckBoardListItem {
  id!: number;
  unitId!: number;
  listingStatus!: string;
  incomingTruckExpenseDTO!: ExpensesDTODetail;
  status!: string;
  driverScheduleAddressId?: number;
  archived!: boolean;
  unit!: UnitDtoDetails;
  driverScheduleAddress!: DriverScheduleAddressDTODetail;
  pickUpLocation!: string;
  expensesId?: number;
  dispatcher?: IdNameModel;
  pickupDate?: Date;

  constructor(json?: IncomingTruckBoardListItem) {
    if (json) {
      Object.assign(this, json);
    }
  }
}

export class IncomingTruckBoardListFilter extends GenericFilterParams {
  deleted = false
}

export class IncomingTruckFilter extends IncomingTruckBoardListFilter {
  treeOperator = TreeOperatorType.NOOP;
  values = [{
    dataType: DataType.ENUM,
    key: 'listingStatus',
    operator: OperatorType.EQUAL,
    value: 'PENDING',
    enumName: 'IncomingTruckListingStatus'
  }];
}

export interface IncomingTruckCreateParams {
  incomingTruckExpenseDTO?: ExpensesDTO;
  unitDTO?: UnitDto;
  id?: number
  unitId?: number;
  status?: string;
  listingStatus?: string;
  driverScheduleAddressDTO?: DriverScheduleAddressDTO;
  sentEmail?: boolean;
  pickupNotes?: string;
  pickupDate?: Date;
}

export interface UnitDto {
  deleted: boolean;
  archived: boolean;
  id: number;
  generalInformation: GeneralInformation;
  internetGroups: IdName[];
  internetOption: InternetOption;
  unitLotLocation: UnitLotLocation;
  unitAssociation: UnitAssociation
  previousOwner: PreviousOwner;
  odometer: Odometer;
  createdDate?: string;
  createdBy?: IdNameModel;
}

export interface UnitAssociation {
  id?: number;
}

export interface GeneralInformation {
  id: number
  year: string;
  vin: string;
  stockNumber: string;
  designationId: number;
  unitStatusId: number;
  assigneeId: number;
  makeId: number;
  unitModelId: number;
  ownerId: number;
  unitTypeId: number;
  unitTypeCategoryId: number;
  enable: boolean;
}

export interface DriverScheduleAddressDTO {
  streetAddress: string;
  city: string;
  state: string;
  zipcode: string;
  location: string;
  latitude: number,
  longitude: number
}

export interface ExpensesDTO {
  poRoNumber: number | string;
  assigneeId: number;
  invoiceNumber: string;
  description: string;
  vendorId: number;
  contactAndVendorAndSupplierType: string,
  crmContactId: number,
  supplierId: number,
  amount: number;
  transDate: string | Date;
  acquisitionMethodId: number,
  expenseId?: number,
  purchasedById: number,
  expensesAttachmentDTO?: ExpensesAttachment[];
}

export interface ExpensesAttachment {
  fullExpensesAttachmentUrl?: string;
  id?: number;
  expensesId?: number;
  url: string;
  file?: File;
  fileName?: string;
}

export enum ModelType {
  MAKE = 'make',
  MODEL = 'model',
  VENDOR = 'vendor',
  CONTACT = 'contact',
  SUPPLIER = 'supplier',
  EXPENSES = 'expense',
  UNIT_TYPE = 'unit-type'
}

export class IncomingTruckDetails {
  id!: number;
  unitId!: number;
  listingStatus!: string;
  driverScheduleAddressId!: number;
  expensesId!: number;
  status!: string;
  pickupDate!: Date;
  archived!: boolean;
  unit!: UnitDtoDetails;
  driverScheduleAddress!: DriverScheduleAddressDTODetail;
  pickUpLocation!: string;
  incomingTruckExpenseDTO!: ExpensesDTODetail;
  sentEmail!: boolean;
  pickupNotes!: string;
}

export class DriverScheduleAddressDTODetail {
  id!: number;
  streetAddress!: string;
  city!: string;
  state!: string;
  zipcode!: string;
  location!: string;
  driverScheduleId!: string
}

export class ExpensesDTODetail {
  id!: number;
  poRoNumber!: string;
  assignee!: IdNameModel;
  invoiceNumber!: string;
  description!: string;
  amount!: number;
  transDate!: string;
  firstExpense!: boolean;
  vendor!: IdNameModel;
  supplier!: IdNameModel;
  expenseType!: IdNameModel;
  financialId!: number;
  unitId!: number;
  expensesAttachmentDTO!: ExpensesAttachment[] | null;
  createdBy!: IdNameModel;
  purchasingAgent!: IdNameModel;
  acquisitionMethod!: IdNameModel;
  crmContact!: IdNameModel;
  ExpensesDTODetail!: string;
  contactAndVendorAndSupplierType!: string;
  expensesId?: number;
  enable!: boolean;
}

export class UnitDtoDetails {
  id!: number;
  archived!: boolean;
  deleted!: boolean;
  generalInformation!: GeneralInformationDetail;
  internetOption!: InternetOption;
  previousOwner!: PreviousOwner;
  internetGroups!: IdName[];
  odometer!: Odometer;
  unitLotLocation!: UnitLotLocation;
  createdDate!: string;
  createdBy!: IdNameModel;
  unitAssociations!: Array<AssociationsUnits>;
  unitAssociation!: AssociationsUnits;
  associations!: Array<AssociationsUnits>
}

export class GeneralInformationDetail {
  id!: number;
  year!: number;
  vin!: string;
  assignee!: IdNameModel;
  stockNumber!: string;
  designation!: IdNameModel;
  unitStatus!: IdNameModel;
  make!: IdNameModel;
  unitModel!: IdNameModel;
  owner!: IdNameModel;
  unitType!: IdNameModel;
  vendor!: IdNameModel;
  unitTypeCategory!: IdNameModel
  enable!: boolean;
}

export enum SelectedStatusEnum {
  IN_TRANSIT = 'In Transit',
  AWAITING_TRANSPORT = 'Awaiting Transport',
  ARRIVED = 'Arrived',
  ISSUE = 'Issue'
}

export const IncomingTruckStatusList = [
  { value: 'AWAITING_TRANSPORT', name: AWAITING_TRANSPORT },
  { value: 'IN_TRANSIT', name: IN_TRANSIT },
  { value: 'ARRIVED', name: 'Arrived' },
  { value: 'ISSUE', name: 'Issue' },
]

export const IncomingTruckStatusListCopy = [
  { value: 'ALL', name: 'All' },
  { value: 'IN_TRANSIT', name: IN_TRANSIT },
  { value: 'AWAITING_TRANSPORT', name: AWAITING_TRANSPORT },
  { value: 'ARRIVED', name: 'Arrived' },
  { value: 'ISSUE', name: 'Issue' },
]

export interface IncomingTruckCommentListItem {
  id: number;
  incomingTruckId: number;
  skeyeUser: IdNameModel;
  comment: string;
  date: string | null;
  name: string;
  mentionUserIds: number[];
}

export interface IncomingTruckCommentCreateParam {
  incomingTruckId: number;
  comment: string;
  id?: number;
  mentionUserIds: number[];
}
