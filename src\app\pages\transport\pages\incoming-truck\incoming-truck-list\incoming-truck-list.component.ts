import { DatePipe, TitleCasePipe } from '@angular/common';
import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { HistoryModuleName, MESSAGES, dateFormat, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { MakeModelService } from '@pages/administration/pages/specification-configration/pages/make-model/make-model.service';
import { UnitTypeService } from '@pages/administration/pages/specification-configration/pages/unit-type/unit-type.service';
import { PrivilegeActionResponseDTOs } from '@pages/auth/models';
import { AuthService } from '@pages/auth/services/auth.service';
import { ColumnDropdownService } from '@pages/common-table-column/column-dropdown/column-dropdown.service';
import { ColumnItem, FilterList, FilterModuleName } from '@pages/common-table-column/models/common-table.column.model';
import { InventoryListItem } from '@pages/inventory/models';
import { InventoryService } from '@pages/inventory/services/inventory.service';
import { FilterMakes, FilterModels, FilterUnitTypes } from '@pages/public-inventories/models';
import { IncomingTruckBoardListFilter, IncomingTruckBoardListItem, IncomingTruckFilter, IncomingTruckStatusList, IncomingTruckStatusListCopy } from '@pages/transport/models/incoming-truck.model';
import { IncomingTruckService } from '@pages/transport/services/incoming-truck.service';
import * as saveAs from 'file-saver';
import { PageChangedEvent } from 'ngx-bootstrap/pagination';
import { ConfirmationService, MenuItem } from 'primeng/api';
import { Subject, debounceTime, takeUntil } from 'rxjs';
import { DataType, FilterValue, IdNameModel, OperatorType, TreeOperatorType } from 'src/app/@shared/models';
import { Utils } from 'src/app/@shared/services';
import { CommonService } from 'src/app/@shared/services/common.service';

@Component({
  selector: 'app-incoming-truck-list',
  templateUrl: './incoming-truck-list.component.html',
  styleUrls: ['./incoming-truck-list.component.scss'],
  providers: [TitleCasePipe, ConfirmationService]
})
export class IncomingTruckListComponent extends BaseComponent implements OnInit {
  incomingTruckList: IncomingTruckBoardListItem[] = [];
  filterParams: IncomingTruckFilter = new IncomingTruckFilter();
  showCreateModal = false;
  showHistoryModal = false;
  historyModuleName = HistoryModuleName.INCOMING_TRUCK;
  showIncomingCreateModal = false;
  selectedIncomingTruck!: IncomingTruckBoardListItem | null;
  driverSchedules: IncomingTruckBoardListItem[] = [];
  statues: IdNameModel[] = [];
  previousStatus!: string;
  isIncomingTruckViewMode = false;
  isEditMode = false;
  showConfirmationDialog = false;
  isViewMode = false;
  inventoryStatuses = IncomingTruckStatusList;
  inventoryStatusesCopy = IncomingTruckStatusListCopy;
  selectedInventory!: IncomingTruckBoardListItem | null;
  inventoryInfo!: InventoryListItem | null;
  cols: any[] = [];
  _selectedColumns: any[] = [];
  showColumnModal = false;
  defaultTabs!: FilterList[];
  tableColumn!: FilterList | undefined;
  defaultColumnsArray: ColumnItem[] = [];
  dropDownColumnList: ColumnItem[] = [];
  filterValue: FilterValue[] = [];
  globalSearch = new Subject<FilterValue[]>();
  initialFilterParams: any;
  makes!: Array<IdNameModel>;
  models!: Array<IdNameModel>;
  unitTypes!: Array<IdNameModel>;
  categoryTypes!: Array<IdNameModel>;
  selectedCategoryId!: number | undefined;
  makeIds!: Array<number>;
  modelIds!: Array<number>;
  unitTypeIds!: Array<number>;
  dropdownLoaders = {
    make: false,
    model: false,
    unitType: false,
    category: false
  }
  userPermissions!: PrivilegeActionResponseDTOs[];
  excelDownloadMenuItems: MenuItem[] = [
    {
      label: "Selected Columns",
      command: () => this.exportUsersToExcel()
    },
    {
      label: "All Columnns",
      command: () => this.exportUsersToExcel(true)
    }
  ];
  activeIndex = 0;

  constructor(private readonly incomingTruckService: IncomingTruckService,
    private readonly inventoryService: InventoryService,
    private readonly toasterService: AppToasterService,
    private readonly confirmationService: ConfirmationService,
    private readonly cdf: ChangeDetectorRef,
    readonly columnDropDownService: ColumnDropdownService,
    private readonly commonService: CommonService,
    private readonly authService: AuthService,
    private readonly datePipe: DatePipe,
    private readonly titleCasePipe: TitleCasePipe,
    private readonly makeModelService: MakeModelService,
    private readonly activeRoute: ActivatedRoute,
    private readonly unitTypeService: UnitTypeService) {
    super();
    this.pageTitle = 'Incoming Truck Tracking';
  }


  async ngOnInit(): Promise<void> {
    await this.getCurrentUser();
    this.getFilterSaveParams();
    this.getFilterDetail();
    this.displaySearchResult();
    this.initialFilterParams = Object.assign([], this.filterParams.values);
    this.userPermissions = this.authService.getRoleInfo()?.privilegeActionResponseDTOs;
    this.activeRoute.queryParams
      .subscribe(params => {
        if (params?.id) {
          this.getIncomingTruckDetails(params.id);
        }
      })
  }

  onPageChanged($event: PageChangedEvent): void {
    this.getAll();
  }

  @Input()
  get selectedColumns(): any[] {
    return this._selectedColumns;
  }

  set selectedColumns(val: any[]) {
    //restore original order
    this._selectedColumns = this.cols.filter(col => val.includes(col));
  }

  private getCurrentUser(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.authService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe({
        next: (user) => {
          if (user) {
            this.currentUser = user;
          }
          resolve();
        },
        error: () => {
          reject();
        }
      });
    });
  }

  downloadPDF(): void {
    if (this.selectedIncomingTruck) {
      if (!this.selectedIncomingTruck?.driverScheduleAddress?.id) {
        this.toasterService.info(MESSAGES.fillAllIncomingTruckData);
        return;
      } else {
        this.incomingTruckService.downloadPdf(`${API_URL_UTIL.driverSchedule.pdf}/${API_URL_UTIL.incomingTruck.generator}/${this.selectedIncomingTruck.id}`).pipe(takeUntil(this.destroy$)).subscribe({
          next: (res: ArrayBuffer) => {
            saveAs(new Blob([res], { type: "application/pdf" }), `Incoming_truck_${this.selectedIncomingTruck?.unit.generalInformation.stockNumber}`);
            this.selectedIncomingTruck = null;
          }
        });
      }
    }
  }

  getAll(): void {
    this.isLoading = true;
    this.filterParams.orderBy = this.orderBy;
    this.incomingTruckService.getListWithFiltersWithPagination<IncomingTruckBoardListFilter, IncomingTruckBoardListItem>(this.filterParams, this.paginationConfig.page, this.paginationConfig.itemsPerPage, API_URL_UTIL.inventory.list)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.incomingTruckList = res.content;
          this.setPaginationParamsFromPageResponse<IncomingTruckBoardListItem>(res);
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
          this.cdf.detectChanges();
        }
      });
  }

  onDelete(truckData: IncomingTruckBoardListItem, event: Event): void {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.deleteWarning.replace('{record}', 'incoming truck data'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.onDeleteConfirmation(truckData);
      }
    });
  }

  onDeleteConfirmation(truckData: IncomingTruckBoardListItem): void {
    this.incomingTruckService.delete(`delete/${truckData.id}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(MESSAGES.incomingTruckDataDeleted);
          this.getAll();
        }
      });
  }

  clearDate(key: string) {
    if (key === "unit.createdDate") {
      this.filterParams.values = this.filterParams.values.filter((param: any) => param.key !== "unit.createdDate");
    } else if (key === "incomingTruckExpenseDTO.transDate") {
      this.filterParams.values = this.filterParams.values.filter((param: any) => param.key !== "transDate");
    } else if (key === "pickupDate") {
      this.filterParams.values = this.filterParams.values.filter((param: any) => param.key !== "pickupDate");
    }
    this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP
    this.getAll();
  }

  storeCurrentValue(status: string): void {
    this.previousStatus = status;
  }

  changeStatus(status: string, incomingTruckDetails: IncomingTruckBoardListItem): void {
    this.showConfirmationDialog = true;
    this.cdf.detectChanges();
    if (status === IncomingTruckStatusList[2].value) {
      this.confirmationService.confirm({
        message: MESSAGES.incomingTruckStatusArrived,
        header: 'Confirmation',
        icon: icons.triangle,
        accept: () => {
          this.changeStatusAPIcall(status, incomingTruckDetails.unitId);
          this.showConfirmationDialog = false;
        },
        reject: () => {
          incomingTruckDetails.status = this.previousStatus;
          this.showConfirmationDialog = false;
        }
      });
    } else {
      this.changeStatusAPIcall(status, incomingTruckDetails.unitId);
    }
  }

  changeStatusAPIcall(selectedStatus: string, unitId: number) {
    const queryParams = `${API_URL_UTIL.incomingTruck.status}/${unitId}?status=${selectedStatus}`;
    this.incomingTruckService.patch(null, queryParams)
      .pipe(takeUntil(this.destroy$)).subscribe(() => {
        this.toasterService.success(MESSAGES.statusChangeSuccess);
        this.getAll();
      })
  }

  onViewEdit(task: IncomingTruckBoardListItem, isEdit: boolean): void {
    this.showIncomingCreateModal = true;
    this.isIncomingTruckViewMode = isEdit ? false : true;
    this.selectedIncomingTruck = task;
  }

  onAdd() {
    this.showIncomingCreateModal = true;
  }

  onAddEditIncomingTruckPopupClose(refreshList: boolean): void {
    this.showIncomingCreateModal = false;
    this.selectedIncomingTruck = null;
    if (refreshList) {
      this.getAll();
    }
  }

  onViewEditClick(inventory: IncomingTruckBoardListItem): void {
    this.showCreateModal = true;
    this.isEditMode = true;
    this.selectedInventory = inventory;
  }

  onViewAssociation(unitId: number) {
    const endpoint = API_URL_UTIL.inventory.inventoryDetails.replace(':unitId', unitId.toString())
    this.inventoryService.get<InventoryListItem>(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
      next: (inventoryDetail) => {
        this.showCreateModal = true;
        this.isViewMode = true
        this.inventoryInfo = inventoryDetail;
      }
    });
  }

  onAddEditPopupClose(refreshList: boolean): void {
    this.showCreateModal = false;
    this.selectedInventory = null;
    this.inventoryInfo = null;
    if (refreshList) {
      this.getAll();
    }
  }

  toggleFilterSidebar() {
    this.showColumnModal = true;
  }

  toggleColumnSidebar() {
    this.showColumnModal = !this.showColumnModal;
  }

  sortColumnList(columns: ColumnItem[]) {
    const tempData: ColumnItem[] = columns.filter(x => x.name === 'Stock' || x.name === 'Associated Stocks');
    const tempData1: ColumnItem[] = columns.filter(x => x.name !== 'Stock' && x.name !== 'Action' && x.name !== 'Associated Stocks');
    const tempData2: ColumnItem[] = columns.filter(x => x.name === 'Action');
    return tempData.concat(tempData1, tempData2);
  }

  sortSelectedColumns() {
    let temp: any[] = [];
    const ids = this.defaultColumnsArray.map(a => a.id);
    this.defaultColumnsArray = this.sortColumnList(this.defaultColumnsArray)
    this._selectedColumns = this.cols = this.defaultColumnsArray;

    if (this.tableColumn?.data) {
      temp = JSON.parse(this.tableColumn?.data);
    }

    this.dropDownColumnList.forEach((column: any) => {
      if (!ids.includes(column.id)) {
        temp.push(column);
      }
    });

    this.dropDownColumnList = temp;
    this.dropDownColumnList = this.sortColumnList(this.dropDownColumnList);
  }

  getFilterSaveParams(): FilterList {
    return {
      module: this.activeIndex === 0 ? FilterModuleName.PENDING_INCOMING_TRUCK_TRACKING.toUpperCase() : FilterModuleName.READY_FOR_PICKUP_INCOMING_TRUCK_TRACKING.toUpperCase(),
      skeyeUserId: this.currentUser?.id ? this.currentUser?.id : null,
      dealerId: this.currentUser?.currentlyActiveDealer?.id ? this.currentUser?.currentlyActiveDealer?.id : null,
      id: this.tableColumn?.id,
      hideField: ''
    }
  }

  getFilterDetail(): void {
    this.defaultColumnsArray = [];
    const userId = this.currentUser?.id ?? null
    const endpoint = `${API_URL_UTIL.filter.filterDataByUserIdAndModule}`.replace(':userId', String(userId)).concat(`?module=${this.activeIndex === 0 ? FilterModuleName.PENDING_INCOMING_TRUCK_TRACKING.toUpperCase() : FilterModuleName.READY_FOR_PICKUP_INCOMING_TRUCK_TRACKING.toUpperCase()}`)
    this.columnDropDownService.getList<FilterList>(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
      next: (filters) => {
        this.defaultTabs = filters.filter((item: any) => item.filterType === "TAB");
        this.tableColumn = filters.find((item: any) => item.filterType === "COLUMN");
        if (this.tableColumn?.data) {
          this.defaultColumnsArray = JSON.parse(this.tableColumn?.data);
          this.getDropDownColumnList();
          this.cdf.detectChanges();
        } else {
          this.getDropDownColumnList(true);
        }
      }
    });
  }

  getDropDownColumnList(shouldCreateDefaultColumn = false) {
    const endpoint = `${API_URL_UTIL.columnMasters.root}${API_URL_UTIL.columnMasters.module}`.concat(`?module=${this.activeIndex === 0 ? FilterModuleName.PENDING_INCOMING_TRUCK_TRACKING.toUpperCase() : FilterModuleName.READY_FOR_PICKUP_INCOMING_TRUCK_TRACKING.toUpperCase()}`);
    this.commonService.getListFromObject<ColumnItem>(endpoint).pipe(takeUntil(this.destroy$))
      .subscribe((response: any) => {
        this.dropDownColumnList = response;
        if (shouldCreateDefaultColumn) {
          this.createDefaultColumns();
        } else {
          this.handleColumnUpdates();
        }
        this.cdf.detectChanges();
      });
  }

  callFilterApiAgain() {
    this.getFilterDetail();
  }

  get filterInfoParams() {
    return {
      id: this.tableColumn?.id,
      module: this.activeIndex === 0 ? FilterModuleName.PENDING_INCOMING_TRUCK_TRACKING.toUpperCase() : FilterModuleName.READY_FOR_PICKUP_INCOMING_TRUCK_TRACKING.toUpperCase(),
      skeyeUserId: this.currentUser?.id ? this.currentUser?.id : null,
      dealerId: this.currentUser?.currentlyActiveDealer?.id ? this.currentUser?.currentlyActiveDealer?.id : null,
      isDefault: false,
      deleted: false,
      isPrivate: true,
      filterName: 'Column filter',
      filterType: 'COLUMN',
      data: JSON.stringify(this.selectedColumns),
      hideField: null
    };
  }

  createDefaultColumns(): void {
    const defaultFields = this.dropDownColumnList.filter(a => a.default ? a.name : null)
    if (this.tableColumn === undefined && defaultFields?.length) {
      this._selectedColumns = defaultFields;
      this.columnDropDownService.add<FilterList>(this.filterInfoParams, '').pipe(takeUntil(this.destroy$)).subscribe({
        next: (res) => {
          if (res.filterType === 'COLUMN') {
            this.tableColumn = res;
            this.defaultColumnsArray = JSON.parse(res.data);
            this.sortSelectedColumns()
            this.cdf.detectChanges();
          }
        }
      });
    }
  }

  private handleColumnUpdates(): void {
    const filterDataColumns = this.defaultColumnsArray;
    const masterDataColumns = this.dropDownColumnList;

    if (filterDataColumns.length === 0 && masterDataColumns.length > 0) {
      this.createDefaultColumns();
      return;
    }
    this.syncFilterWithMaster();
  }

  private syncFilterWithMaster(): void {
    const newColumns = this.syncColumns(
      this.defaultColumnsArray,
      this.dropDownColumnList
    );

    const hasChanges = this.hasChanges(this.defaultColumnsArray, newColumns);

    if (hasChanges) {
      this.updateFilterData(newColumns);
    } else {
      this.sortSelectedColumns();
    }
  }

  private syncColumns(filterColumns: ColumnItem[], masterColumns: ColumnItem[]): ColumnItem[] {
    const result: ColumnItem[] = [];

    // Keep existing columns that still exist in master
    filterColumns.forEach(filterCol => {
      const masterCol = masterColumns.find(m => m.key === filterCol.key);
      if (masterCol) {
        result.push({ ...masterCol });
      }
    });

    // Add new default columns from master that don't exist in filter
    masterColumns.forEach(masterCol => {
      const existsInResult = result.some(r => r.key === masterCol.key);
      if (!existsInResult && masterCol.default) {
        result.push({ ...masterCol });
      }
    });

    return result;
  }

  private hasChanges(oldArray: ColumnItem[], newArray: ColumnItem[]): boolean {
    if (oldArray.length !== newArray.length) {
      return true;
    }

    for (let i = 0; i < oldArray.length; i++) {
      if (JSON.stringify(oldArray[i]) !== JSON.stringify(newArray[i])) {
        return true;
      }
    }

    return false;
  }

  private updateFilterData(newColumns: ColumnItem[]): void {
    if (this.tableColumn?.id) {
      this.updateDefaultColumns(newColumns);
    }

    this.defaultColumnsArray = newColumns;
    this.sortSelectedColumns();
  }

  private updateDefaultColumns(newColumns: ColumnItem[]): void {
    const params = {
      ...this.filterInfoParams,
      data: JSON.stringify(newColumns)
    };
    this.columnDropDownService.update<FilterList>(params).pipe(takeUntil(this.destroy$)).subscribe();
  }

  // Search Implementation

  assignDataType(col: ColumnItem): string {
    let stringDataType = '';
    switch (col?.type) {
      case 'boolean':
        stringDataType = DataType.BOOLEAN
        break;
      case 'INTEGER':
        stringDataType = DataType.INTEGER
        break;
      case 'DROP_DOWN':
        stringDataType = col?.shortingKey === 'status' ? DataType.ENUM : DataType.INTEGER
        break;
      case 'MULTI_DROP_DOWN':
        stringDataType = DataType.LONG
        break;
      case 'DATE':
        stringDataType = DataType.DATE
        break;
      case 'DOUBLE':
        stringDataType = DataType.DOUBLE
        break;
      default:
        stringDataType = DataType.STRING
        break;
    }
    return stringDataType
  }

  assignOperator(type: string) {
    let operatorType = '';
    switch (type) {
      case 'INTEGER':
        operatorType = OperatorType.EQUAL
        break;
      case 'DATE':
        operatorType = OperatorType.GREATER_THAN_OR_EQUAL
        break;
      case 'DROP_DOWN':
        operatorType = OperatorType.EQUAL
        break;
      case 'DOUBLE':
        operatorType = OperatorType.EQUAL
        break;
      case 'MULTI_DROP_DOWN':
        operatorType = OperatorType.IN
        break;
      default:
        operatorType = OperatorType.LIKE
        break;
    }
    return operatorType
  }


  getEventValue(event: any, col: any) {
    let temp = '';
    switch (col.type) {
      case 'DROP_DOWN':
        temp = col?.shortingKey === 'status' ? event?.value?.split(' ')?.join('_')?.toUpperCase() : event?.value;
        break;
      case 'DATE':
        const endDate: any = this.datePipe.transform(event, dateFormat.format);
        temp = this.setStartDate(new Date(endDate).toISOString())
        break;
      case 'MULTI_DROP_DOWN':
        temp = event?.value;
        break;
      default:
        temp = event.value
        break;
    }
    return temp
  }

  getFilterInfo(inputValue: any, col: any) {
    this.filterParams.values = this.filterParams.values === undefined ? [] : this.filterParams.values
    const existingValue = this.filterParams.values.find((f: any) => f.key === col.shortingKey)
    if (!existingValue && inputValue) {
      this.filterParams.values.push({
        dataType: this.assignDataType(col) as DataType,
        key: col.shortingKey,
        operator: this.assignOperator(col.type) as OperatorType,
        value: inputValue,
        enumName: col.enumKey
      })
    } else {
      if (existingValue) {
        if (inputValue?.length) {
          existingValue.value = inputValue;
        } else {
          this.filterParams.values.splice(this.filterParams.values.indexOf(existingValue), 1)
        }
      }
    }
    if (col.type === 'DROP_DOWN' && (inputValue === 'ALL' || inputValue === '')) {
      const rm = this.filterParams.values.find(d => d.key === 'status')
      if (rm) {
        this.filterParams.values.splice(this.filterParams.values.indexOf(rm), 1)
      }
    }
  }

  tableSearchByColumn(event: any, col: any) {
    this.isLoading = true;
    this.incomingTruckList = [];
    if (col.shortingKey === 'unit.generalInformation.unitTypeCategory.id') {
      this.onCategoryChange();
      this.filterParams.values = this.filterParams?.values?.filter((value: FilterValue) => {
        return value.key !== "unit.generalInformation.unitType.id" &&
          value.key !== "unit.generalInformation.make.id" &&
          value.key !== "unit.generalInformation.model.id"
      });
    }
    const searchInput = this.getEventValue(event, col)
    this.getFilterInfo(searchInput, col)
    this.setSearchEndDate(event, col);
    this.globalSearch.next(this.filterParams.values);
    this.setValueForReset(searchInput, col);
  }

  setValueForReset(input: string, col: any) {
    const temp = this.selectedColumns.find(d => d.key === col.key);
    const temp1 = this.dropDownColumnList.find(d => d.key === col.key);

    if (col.type === 'DATE') {
      temp.value = this.datePipe.transform(input, 'MM/dd/yyyy')
    } else if (col.key === 'status') {
      temp.value = this.titleCasePipe.transform(input?.split('_')?.join(' '));
    } else if (col.shortingKey === 'make.id') {
      temp.value = this.makeIds;
    } else if (col.shortingKey === 'unitModel.id') {
      temp.value = this.modelIds;
    } else if (col.shortingKey === 'unitType.id') {
      temp.value = this.unitTypeIds;
    } else {
      temp.value = input;
    }

    if (temp1) {
      temp1.value = temp.value;
    }

  }

  setSearchEndDate(event: any, col: any) {

    const existingDate = this.filterParams.values.find(d => d.key === col.shortingKey && d.operator === OperatorType.LESS_THAN_OR_EQUAL)
    if (existingDate) {
      this.filterParams.values.splice(this.filterParams.values.indexOf(existingDate), 1)
    }
    if (col.type === 'DATE') {
      const startDate: any = this.datePipe.transform(event, dateFormat.format)
      this.filterParams.values.push({
        dataType: this.assignDataType(col) as DataType,
        key: col.shortingKey,
        operator: OperatorType.LESS_THAN_OR_EQUAL,
        value: this.setEndDate(new Date(startDate).toISOString()),
        enumName: col.enumKey
      })
    }
  }

  displaySearchResult() {
    this.globalSearch.pipe(debounceTime(1000)).subscribe(() => {
      this.isLoading = true;
      this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP
      this.getAll();
    });
  }

  setEndDate(endDate: string): string {
    const date = new Date(endDate);
    return new Date(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(), 23, 59, 50).toISOString();
  }

  setStartDate(endDate: string): string {
    const date = new Date(endDate);
    return new Date(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(), 0, 0, 0).toISOString();
  }

  clearSearchInput() {
    this.selectedColumns.forEach(cols => {
      cols.value = null;
    });

    this.dropDownColumnList.forEach(cols => {
      cols.value = null;
    });
    this.selectedCategoryId = undefined;
    this.makeIds = this.modelIds = this.unitTypeIds = [];
    this.makes = this.models = this.unitTypes = [];
    this.filterParams.values = Object.assign([], this.initialFilterParams);
    if (this.activeIndex === 1) {
      this.filterParams.values[0].value = 'AVAILABLE';
    } else {
      this.filterParams.values[0].value = 'PENDING';
    }
    this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP
    this.getAll();
  }

  exportUsersToExcel(downloadAll = false): void {
    this.isExporting = true;
    this.incomingTruckService.getListWithFiltersWithPagination<IncomingTruckBoardListFilter, IncomingTruckBoardListItem>
      (this.filterParams, 1, this.paginationConfig.totalElements, API_URL_UTIL.inventory.list)
      .pipe(takeUntil(this.destroy$)).subscribe(res => {
        import("xlsx").then(xlsx => {
          const inventory = this.getExcelData(res.content, downloadAll)
          const worksheet = xlsx.utils.json_to_sheet(inventory);
          const workbook = { Sheets: { 'data': worksheet }, SheetNames: ['data'] };
          const excelBuffer: any = xlsx.write(workbook, { bookType: 'xlsx', type: 'array' });
          Utils.saveAsExcelFile(excelBuffer, "Incoming-trucks");
          this.isExporting = false;
        });
      });
  }

  getExcelData(incomingTruck: Array<IncomingTruckBoardListItem>, downloadAll = false) {
    let excelData: any[] = [];
    if (incomingTruck[0].listingStatus === 'PENDING') {
      excelData = incomingTruck.map(res => ({
        'VIN': res?.unit?.generalInformation?.vin,
        'PO/RO': res?.incomingTruckExpenseDTO?.poRoNumber,
        'Invoice': res?.incomingTruckExpenseDTO?.invoiceNumber,
        'Acquisition Cost': Utils.formatCurrency(res?.incomingTruckExpenseDTO?.amount),
        'Purchased By': res?.incomingTruckExpenseDTO?.purchasingAgent?.name,
        'Owner': res?.unit?.generalInformation?.owner?.name,
        'Unit Type': res?.unit?.generalInformation?.unitType?.name,
        'Unit Model': res?.unit?.generalInformation?.unitModel?.name,
        'Make': res?.unit?.generalInformation?.make?.name,
        'Created By': res?.unit?.createdBy?.name,
        'Creation Date': Utils.dateIntoUserReadableFormat(res?.unit?.createdDate ?? ''),
        'Category': res?.unit?.generalInformation?.unitTypeCategory?.name,
      }));
    } else {
      excelData = incomingTruck.map(res => ({
        'Stock': res?.unit?.generalInformation?.stockNumber,
        'Status': res?.status,
        'Created By': res?.unit?.createdBy?.name,
        'Creation Date': Utils.dateIntoUserReadableFormat(res?.unit?.createdDate ?? ''),
        'Pick Up Location Detail': res?.pickUpLocation,
        'Acquisition Cost': Utils.formatCurrency(res?.incomingTruckExpenseDTO?.amount),
        'Vendor': res?.incomingTruckExpenseDTO?.vendor?.name,
        'Owner': res?.unit?.generalInformation?.owner?.name,
        'Unit Type': res?.unit?.generalInformation?.unitType?.name,
        'Make': res?.unit?.generalInformation?.make?.name,
        'Unit Model': res?.unit?.generalInformation?.unitModel?.name,
        'Vin': res?.unit?.generalInformation?.vin,
        'Year': res?.unit?.generalInformation?.year,
        'Category': res?.unit?.generalInformation?.unitTypeCategory?.name,
        'Purchase Date': Utils.dateIntoUserReadableFormat(new Date(res?.incomingTruckExpenseDTO?.transDate).toLocaleDateString().replace(/\//g, '-') ?? ''),
        'Pickup Date': res?.pickupDate && Utils.dateIntoUserReadableFormat(new Date(res?.pickupDate).toLocaleDateString().replace(/\//g, '-') ?? ''),
      }));
    }
    if (!downloadAll) {
      const selectedKeysToBeDisplayedInExcel = this.defaultColumnsArray.map(({ name }) => name);
      for (const [index, data] of excelData.entries()) {
        for (const key in data) {
          if (!selectedKeysToBeDisplayedInExcel.includes(key)) {
            delete (excelData as any)[index][key]
          }
        }
      }
    }

    return excelData;
  }

  getMake(): void {
    if (!this.makes?.length) {
      this.dropdownLoaders.make = true;
      this.makeModelService.get<Array<FilterMakes>>(`${API_URL_UTIL.inventory.list}?categoryId=${this.selectedCategoryId ?? ''}`)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (res: Array<FilterMakes>) => {
            this.makes = res;
            this.dropdownLoaders.make = false;
            this.cdf.detectChanges();
          }
        });
    }
  }

  getModels(makeIds?: Array<number>, callApi = false): void {
    if (!this.models?.length || callApi) {
      this.dropdownLoaders.model = true;
      this.unitTypeService.add<Array<FilterModels>>((makeIds?.length ? makeIds : undefined) as any, `${API_URL_UTIL.inventory.models}/${API_URL_UTIL.inventory.list}`)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (res: Array<FilterModels>) => {
            this.models = res;
            this.dropdownLoaders.model = false;
            this.cdf.detectChanges();
          }
        });
    }
  }

  getUnitType(): void {
    if (!this.unitTypes?.length) {
      this.dropdownLoaders.unitType = true
      this.unitTypeService.get<Array<FilterUnitTypes>>(`types/${API_URL_UTIL.inventory.list}?categoryId=${this.selectedCategoryId ?? ''}`)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (res: Array<FilterUnitTypes>) => {
            this.unitTypes = res;
            this.dropdownLoaders.unitType = false;
            this.cdf.detectChanges();
          }
        });
    }
  }

  getCategoryTypes() {
    if (!this.categoryTypes?.length) {
      this.dropdownLoaders.category = true
      this.inventoryService.getCategoryType().pipe(takeUntil(this.destroy$)).subscribe({
        next: (categoryTypes: Array<IdNameModel>) => {
          this.categoryTypes = categoryTypes;
          this.dropdownLoaders.category = false;
          this.cdf.detectChanges();
        }
      });
    }
  }

  clearMakes(col: ColumnItem): void {
    this.makeIds = [];
    this.getModels([], true)
    this.tableSearchByColumn([], col);
  }

  clearModels(col: ColumnItem): void {
    this.modelIds = [];
    this.tableSearchByColumn([], col);
  }

  clearUnitTypes(col: ColumnItem): void {
    this.unitTypeIds = [];
    this.tableSearchByColumn([], col);
  }

  onCategoryChange() {
    this.makes = this.unitTypes = this.models = [];
    this.makeIds = this.unitTypeIds = this.modelIds = [];
  }

  getIncomingTruckDetails(id: string): void {
    this.incomingTruckService.get<IncomingTruckBoardListItem>(id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: IncomingTruckBoardListItem) => {
          this.onViewEdit(res, false);
        }
      });
  }

  onTabChanged(e: any) {
    this.filterParams = new IncomingTruckFilter();
    if (e.index === 1) {
      this.filterParams.values[0].value = 'AVAILABLE';
    }
    this.selectedColumns = [];
    this.getAll();
    this.getFilterSaveParams();
    this.getFilterDetail();
  }

  toggleHistorySidebar() {
    this.showHistoryModal = !this.showHistoryModal;
  }
}
