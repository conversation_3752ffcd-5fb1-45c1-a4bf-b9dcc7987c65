import { NgxGpAutocompleteModule } from '@angular-magic/ngx-gp-autocomplete';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { UiSwitchConfig } from '@constants/*';
import { environment } from '@env/environment';
import { Loader } from '@googlemaps/js-api-loader';
import { SuppliersModule } from '@pages/administration/pages/suppliers/suppliers.module';
import { VendorsModule } from '@pages/administration/pages/vendors/vendors.module';
import { ColumnDropdownModule } from '@pages/common-table-column/column-dropdown.module';
import { CrmContactCustomerAddModule } from '@pages/crm/pages/crm-customer/crm-contact-customer-add/crm-contact-customer-add.module';
import { InventoryAddWrapperModule } from '@pages/inventory/pages/inventory-add-wrapper/inventory-add-wrapper.module';
import { AddNewMakeModule } from '@pages/inventory/pages/inventory-add/inventory-general-tab/add-new-make/add-new-make.module';
import { AddNewModelModule } from '@pages/inventory/pages/inventory-add/inventory-general-tab/add-new-model/add-new-model.module';
import { AddNewUnitTypeModule } from '@pages/inventory/pages/inventory-add/inventory-general-tab/add-new-unit-type/add-new-unit-type.module';
import { GoogleMapModule } from '@sharedComponents/google-map/google-map.component.module';
import { SharedComponentsModule } from '@sharedComponents/shared-components.module';
import { FontAwesomeIconsModule } from '@sharedModules/*';
import { MentionModule } from 'angular-mentions';
import { TabsModule } from 'ngx-bootstrap/tabs';
import { UiSwitchModule } from 'ngx-ui-switch';
import { ConfirmationService } from 'primeng/api';
import { AvatarModule } from 'primeng/avatar';
import { CalendarModule } from 'primeng/calendar';
import { CardModule } from 'primeng/card';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { DialogModule } from 'primeng/dialog';
import { DividerModule } from 'primeng/divider';
import { DropdownModule } from 'primeng/dropdown';
import { EditorModule } from 'primeng/editor';
import { InputNumberModule } from 'primeng/inputnumber';
import { MenuModule } from 'primeng/menu';
import { MessageModule } from 'primeng/message';
import { MultiSelectModule } from 'primeng/multiselect';
import { RadioButtonModule } from 'primeng/radiobutton';
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { TabViewModule } from 'primeng/tabview';
import { DirectivesModule } from 'src/app/@shared/directives/directives.module';
import { IncomingTruckAddWrapperComponent } from './incoming-truck-add-wrapper/incoming-truck-add-wrapper.component';
import { AcquisitionInfoComponent } from './incoming-truck-add/acquisition-info/acquisition-info.component';
import { CommunicationComponent } from './incoming-truck-add/communication/communication.component';
import { IncomingTruckAddComponent } from './incoming-truck-add/incoming-truck-add.component';
import { PaymentInfoComponent } from './incoming-truck-add/payment-info/payment-info.component';
import { PickupInfoComponent } from './incoming-truck-add/pickup-info/pickup-info.component';
import { IncomingTruckCommentComponent } from './incoming-truck-comment/incoming-truck-comment.component';
import { IncomingTruckListComponent } from './incoming-truck-list/incoming-truck-list.component';
import { IncomingTruckRoutingModule } from './incoming-truck-routing.module';
import { IncomingTruckSearchComponent } from './incoming-truck-search/incoming-truck-search.component';
import { IncomingTruckComponent } from './incoming-truck.component';

@NgModule({
  declarations: [
    IncomingTruckComponent,
    IncomingTruckListComponent,
    IncomingTruckAddWrapperComponent,
    IncomingTruckSearchComponent,
    IncomingTruckAddComponent,
    IncomingTruckCommentComponent,
    PickupInfoComponent,
    AcquisitionInfoComponent,
    CommunicationComponent,
    PaymentInfoComponent,
  ],
  imports: [
    CommonModule,
    IncomingTruckRoutingModule,
    SharedComponentsModule,
    DirectivesModule,
    FontAwesomeIconsModule,
    TableModule,
    DropdownModule,
    ScrollingModule,
    MultiSelectModule,
    FormsModule,
    SidebarModule,
    ReactiveFormsModule,
    ConfirmPopupModule,
    RadioButtonModule,
    CalendarModule,
    UiSwitchModule.forRoot(UiSwitchConfig),
    MessageModule,
    VendorsModule,
    SuppliersModule,
    AddNewModelModule,
    AddNewMakeModule,
    InventoryAddWrapperModule,
    GoogleMapModule,
    NgxGpAutocompleteModule,
    ColumnDropdownModule,
    AddNewUnitTypeModule,
    DialogModule,
    CrmContactCustomerAddModule,
    MenuModule,
    TabsModule.forRoot(),
    TabViewModule,
    MentionModule,
    EditorModule,
    CheckboxModule,
    DividerModule,
    AvatarModule,
    CardModule,
    ConfirmDialogModule,
    InputNumberModule
  ],
  providers: [ConfirmationService, {
    provide: Loader,
    useValue: new Loader({
      apiKey: environment.googleMapApiKey,
      libraries: ['places']
    })
  }]
})
export class IncomingTruckModule { }
