// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

import { versions } from './versions';

export const environment = {
  versions,
  production: false,
  apiUrl: 'http://192.168.1.145:8080/api',
  persistUserSession: true, // if the user login session needs to be persisted across browser tabs
  sentryKey: '',
  enableOtpBasedLogin: false,// true means user will be able to login with otp else will have to login with email & password only
  showButtonToAddDummyData: true,
  googleMapApiKey: 'AIzaSyC928Cd5M-PyrNF0nQeDzLWTv7gJjNrIeE',
  firebase: {
    apiKey: "AIzaSyCqOeMGHnMnpY55tD-OO51rmHF7tB2RyX8",
    authDomain: "skeyedeveloper-447ea.firebaseapp.com",
    projectId: "skeyedeveloper-447ea",
    storageBucket: "skeyedeveloper-447ea.appspot.com",
    messagingSenderId: "934556463127",
    appId: "1:934556463127:web:9af91145eb2420fc7cac52"
  },
  forntendUrl: 'http://localhost:4200/'
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
